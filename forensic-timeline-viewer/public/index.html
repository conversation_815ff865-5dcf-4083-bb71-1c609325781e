<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='.9em' font-size='90'%3E🔍%3C/text%3E%3C/svg%3E">
    <title>Forensic Timeline Viewer</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">


    <link rel="stylesheet" href="styles.css?v=36">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">Forensic Timeline Viewer</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" id="table-tab">Table View</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="tagged-tab">Tagged Events <span id="tagged-count" class="badge bg-primary">0</span></a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="#" id="files-tab">Files</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Upload Forensic Data</h5>
                    </div>
                    <div class="card-body">
                        <form id="upload-form" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="file" class="form-label">JSON or ZIP File</label>
                                <input class="form-control" type="file" id="file" name="file" accept=".json,.zip">
                                <small class="form-text text-muted">Upload a JSON file or a ZIP archive containing JSON files</small>
                            </div>
                            <div class="d-flex gap-2 flex-wrap">
                                <button type="submit" class="btn btn-primary">Upload</button>
                                <button type="button" id="load-sample-btn" class="btn btn-secondary">Load Sample Data</button>
                                <button type="button" id="parse-local-folder-btn" class="btn btn-info">Parse Local Folder</button>
                                <button type="button" id="erase-all-data-btn" class="btn btn-danger">
                                    <i class="bi bi-trash3"></i> Erase All Data & Files
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Search</h5>
                    </div>
                    <div class="card-body">
                        <!-- Search Options and Date Search - Above Search Bar -->
                        <div class="d-flex justify-content-between align-items-center mb-2 search-controls">
                            <div class="d-flex gap-2">
                                <!-- Search Options Dropdown -->
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" id="search-options-btn" title="Search Options">
                                        <i class="bi bi-gear"></i> Options
                                    </button>
                                    <div class="dropdown-menu p-3" style="width: 300px;">
                                        <h6 class="dropdown-header">Search Options</h6>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="regex-search-checkbox">
                                            <label class="form-check-label" for="regex-search-checkbox">
                                                Use Regex
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="case-sensitive-checkbox">
                                            <label class="form-check-label" for="case-sensitive-checkbox">
                                                Case Sensitive
                                            </label>
                                        </div>
                                        <div class="mb-3">
                                            <label for="search-field" class="form-label">Search in Field:</label>
                                            <select class="form-select" id="search-field">
                                                <option value="all">All Fields</option>
                                                <option value="timestamp">Timestamp</option>
                                                <option value="event_type">Event Type</option>
                                                <option value="source">Source</option>
                                                <option value="file_name">Artifact Name</option>
                                                <option value="description">Description</option>
                                                <option value="details">Details (JSON Content)</option>
                                            </select>
                                        </div>
                                        <div class="dropdown-divider"></div>
                                        <h6 class="dropdown-header">Search Help</h6>
                                        <small class="text-muted">
                                            <ul class="ps-3 mb-0">
                                                <li>Use <code>%</code> for wildcard search (e.g., <code>%log%</code>)</li>
                                                <li>Use <code>/pattern/</code> for regex search</li>
                                                <li>Use quotes for exact phrase: <code>"exact phrase"</code></li>
                                                <li>For JSON details, search for keys or values: <code>%"key":%</code> or <code>%"value"%</code></li>
                                                <li><strong>Note:</strong> Search will only show matching events</li>
                                            </ul>
                                        </small>
                                    </div>
                                </div>

                                <!-- Date Search Button -->
                                <button class="btn btn-primary" type="button" id="date-search-btn" title="Search by Date & Time">
                                    <i class="bi bi-calendar"></i> Date Search
                                </button>
                            </div>

                            <!-- Search Status/Info -->
                            <small class="text-muted" id="search-status">
                                Use options above to configure your search
                            </small>
                        </div>

                        <!-- Main Search Form -->
                        <form id="search-form">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" id="search-input" placeholder="Search events..." list="search-history">
                                <datalist id="search-history">
                                    <!-- Search history will be populated here -->
                                </datalist>
                                <button class="btn btn-primary" type="submit" title="Search">
                                    <i class="bi bi-search"></i> Search
                                </button>
                            </div>

                            <!-- Date Search Panel (initially hidden) -->
                            <div id="date-search-panel" class="mt-2 p-3 border rounded" style="display: none;">
                                <h6 class="text-white">Search by Date & Time</h6>

                                <!-- Start Date & Time -->
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <label class="form-label text-white"><strong>Start Date & Time:</strong></label>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="date-search-start" class="form-label text-white">Date:</label>
                                        <input type="date" class="form-control" id="date-search-start">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="start-hour" class="form-label text-white">Hour:</label>
                                        <input type="number" class="form-control" id="start-hour" min="0" max="23" placeholder="00">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="start-minute" class="form-label text-white">Min:</label>
                                        <input type="number" class="form-control" id="start-minute" min="0" max="59" placeholder="00">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="start-second" class="form-label text-white">Sec:</label>
                                        <input type="number" class="form-control" id="start-second" min="0" max="59" placeholder="00">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="start-millisecond" class="form-label text-white">Ms:</label>
                                        <input type="number" class="form-control" id="start-millisecond" min="0" max="999" placeholder="000">
                                    </div>
                                </div>

                                <!-- End Date & Time -->
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <label class="form-label text-white"><strong>End Date & Time:</strong></label>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="date-search-end" class="form-label text-white">Date:</label>
                                        <input type="date" class="form-control" id="date-search-end">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="end-hour" class="form-label text-white">Hour:</label>
                                        <input type="number" class="form-control" id="end-hour" min="0" max="23" placeholder="23">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="end-minute" class="form-label text-white">Min:</label>
                                        <input type="number" class="form-control" id="end-minute" min="0" max="59" placeholder="59">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="end-second" class="form-label text-white">Sec:</label>
                                        <input type="number" class="form-control" id="end-second" min="0" max="59" placeholder="59">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="end-millisecond" class="form-label text-white">Ms:</label>
                                        <input type="number" class="form-control" id="end-millisecond" min="0" max="999" placeholder="999">
                                    </div>
                                </div>

                                <!-- Quick Time Presets -->
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <label class="form-label text-white"><strong>Quick Presets:</strong></label>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-outline-info btn-sm" id="preset-today">Today</button>
                                            <button type="button" class="btn btn-outline-info btn-sm" id="preset-yesterday">Yesterday</button>
                                            <button type="button" class="btn btn-outline-info btn-sm" id="preset-last-7-days">Last 7 Days</button>
                                            <button type="button" class="btn btn-outline-info btn-sm" id="preset-last-30-days">Last 30 Days</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="d-flex justify-content-end mt-2">
                                    <button type="button" class="btn btn-secondary btn-sm me-2" id="date-search-cancel">
                                        <i class="bi bi-x-circle"></i> Cancel
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm me-2" id="set-current-time">
                                        <i class="bi bi-clock"></i> Set Current Time
                                    </button>
                                    <button type="button" class="btn btn-primary btn-sm" id="date-search-apply">
                                        <i class="bi bi-search"></i> Apply Date & Time Filter
                                    </button>
                                </div>

                                <!-- Help Text -->
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        Leave time fields empty to search entire day. Milliseconds are optional for precise filtering.
                                    </small>
                                </div>
                            </div>

                            <div id="search-tags" class="mb-3">
                                <!-- Active search filters will appear here -->
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sort-field" class="form-label">Sort By</label>
                                        <select class="form-select" id="sort-field">
                                            <option value="timestamp">Timestamp</option>
                                            <option value="event_type">Event Type</option>
                                            <option value="source">Source</option>
                                            <option value="file_name">Artifact Name</option>
                                            <option value="description">Description</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sort-order" class="form-label">Order</label>
                                        <select class="form-select" id="sort-order">
                                            <option value="asc">Ascending</option>
                                            <option value="desc">Descending</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Main content area (70%) -->
            <div class="col-lg-8" id="main-content-area">
                <div id="table-view" class="view-container">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5>Table View</h5>
                            <button class="btn btn-sm btn-outline-primary" id="column-select-btn">
                                <i class="bi bi-gear"></i> Select Columns
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead id="events-table-head">
                                        <tr>
                                            <th>Timestamp</th>
                                            <th>Event Type</th>
                                            <th>Source</th>
                                            <th>Description</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="events-table-body">
                                        <!-- Events will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                            <div id="loading-more" class="text-center py-3" style="display: none;">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Loading more events...</span>
                                </div>
                                <span class="ms-2">Loading more events...</span>
                            </div>
                            <div class="text-center mt-3">
                                <button id="load-more-btn" class="btn btn-primary" style="display: none;">Load More</button>
                            </div>
                            <div id="showing-info" class="text-center mt-3 mb-2">
                                <div class="d-flex justify-content-center align-items-center">
                                    <div class="text-white">Showing 0 of 0 events</div>
                                </div>
                                <div class="progress mt-2" style="height: 10px;">
                                    <div id="progress-bar" class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mt-1">
                                    <div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="pagination-toggle" checked>
                                            <label class="form-check-label text-white" for="pagination-toggle">Use Pagination (<span id="records-per-page">50</span> per page)</label>
                                        </div>
                                    </div>
                                    <div>
                                        <small class="text-white" id="loaded-percentage">0% loaded</small>
                                        <small class="text-white ms-2" id="loaded-pages">0 pages loaded</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Pagination Controls -->
                            <nav aria-label="Event pagination" id="pagination-controls" class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="pagination-info text-white">
                                        Page <span id="current-page-num">1</span> of <span id="total-pages-num">1</span>
                                    </div>
                                    <ul class="pagination pagination-sm mb-0">
                                        <li class="page-item disabled" id="pagination-first">
                                            <a class="page-link" href="#" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item disabled" id="pagination-prev">
                                            <a class="page-link" href="#" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                                        <li class="page-item" id="pagination-next">
                                            <a class="page-link" href="#" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item" id="pagination-last">
                                            <a class="page-link" href="#" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                    </ul>
                                    <div class="pagination-jump">
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" id="page-jump-input" min="1" value="1">
                                            <button class="btn btn-outline-secondary" type="button" id="page-jump-btn">Go</button>
                                        </div>
                                    </div>
                                </div>
                            </nav>
                        </div>
                    </div>
                </div>

                <div id="tagged-view" class="view-container" style="display: none;">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5>Tagged Events</h5>
                            <button class="btn btn-sm btn-outline-danger" id="clear-tags-btn">
                                <i class="bi bi-trash"></i> Clear All Tags
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead id="tagged-table-head">
                                        <tr>
                                            <th>Timestamp</th>
                                            <th>Event Type</th>
                                            <th>Source</th>
                                            <th>Artifact Name</th>
                                            <th>Description</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tagged-table-body">
                                        <!-- Tagged events will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                            <div id="no-tagged-events" class="text-center py-5">
                                <i class="bi bi-tag" style="font-size: 2rem;"></i>
                                <p class="mt-3">No tagged events yet. Use the 'm' key to tag events in the main view.</p>
                            </div>
                        </div>
                    </div>
                </div>







                <div id="files-view" class="view-container" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5>Uploaded Files</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <button id="refresh-files-btn" class="btn btn-primary">Refresh Files</button>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Filename</th>
                                            <th>Size</th>
                                            <th>Modified</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="files-table-body">
                                        <!-- Files will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Details panel (30%) -->
            <div class="col-lg-4" id="details-panel">
                <div class="card sticky-top" style="top: 10px; max-height: calc(100vh - 20px); overflow-y: auto;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>Event Details</h5>
                        <button class="btn btn-sm btn-outline-secondary" id="close-details-btn" style="display: none;">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="details-placeholder" class="text-center text-muted py-5">
                            <i class="bi bi-info-circle" style="font-size: 2rem;"></i>
                            <p class="mt-3">Select an event to view its details</p>
                        </div>
                        <div id="event-details-panel" style="display: none;">
                            <!-- Event details will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
    </div>

    <!-- Event Details Modal -->
    <div class="modal fade" id="event-details-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Event Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="event-details-content">
                        <!-- Event details will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- File Content Modal -->
    <div class="modal fade" id="file-content-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="file-content-title">File Content</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary" id="view-as-text-btn">View as Text</button>
                            <button type="button" class="btn btn-outline-secondary" id="view-as-json-btn">View as JSON</button>
                            <button type="button" class="btn btn-outline-secondary" id="view-as-table-btn">View as Table</button>
                        </div>
                    </div>
                    <div id="file-content-container">
                        <div id="text-view">
                            <pre id="file-content-text" class="border p-3" style="max-height: 500px; overflow: auto;"></pre>
                        </div>
                        <div id="json-view" style="display: none;">
                            <pre id="file-content-json" class="border p-3" style="max-height: 500px; overflow: auto;"></pre>
                        </div>
                        <div id="table-view-container" style="display: none;">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead id="file-content-table-head">
                                        <!-- Table headers will be populated here -->
                                    </thead>
                                    <tbody id="file-content-table-body">
                                        <!-- Table rows will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Column Selection Modal -->
    <div class="modal fade" id="column-select-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Select Columns</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="select-all-columns" checked>
                            <label class="form-check-label" for="select-all-columns">
                                <strong>Select All</strong>
                            </label>
                        </div>
                    </div>
                    <div id="column-checkboxes" class="mb-3">
                        <!-- Column checkboxes will be populated here -->
                    </div>
                    <div class="mb-3">
                        <label for="detail-fields-select" class="form-label">Add Detail Fields:</label>
                        <select class="form-select" id="detail-fields-select">
                            <option value="">Select a detail field to add...</option>
                            <!-- Detail fields will be populated here -->
                        </select>
                    </div>
                    <div id="selected-detail-fields">
                        <!-- Selected detail fields will be displayed here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="window.forceCloseModal()" title="Force close if modal is stuck">Force Close</button>
                    <button type="button" class="btn btn-primary" id="apply-columns-btn">Apply</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Folder Selection Modal -->
    <div class="modal fade" id="folder-select-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Parse Local Folder</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="folder-path" class="form-label">Folder Path:</label>
                        <input type="text" class="form-control" id="folder-path" placeholder="Enter folder path">
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="recursive-checkbox" checked>
                        <label class="form-check-label" for="recursive-checkbox">
                            Parse recursively (include subfolders)
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="parse-folder-btn">Parse Folder</button>
                </div>
            </div>
        </div>
    </div>



    <!-- Filter Dropdown Template (will be cloned and used for each column) -->
    <div id="filter-dropdown-template" class="filter-dropdown" style="display: none;">
        <div class="filter-dropdown-header">
            <span class="filter-title">Filter</span>
            <button type="button" class="btn-close btn-close-sm filter-close" aria-label="Close"></button>
        </div>
        <div class="filter-dropdown-body">
            <input type="text" class="filter-search" placeholder="Search...">
            <div class="form-check mb-2">
                <input class="form-check-input filter-select-all" type="checkbox" checked>
                <label class="form-check-label">Select All</label>
            </div>
            <ul class="filter-list">
                <!-- Filter items will be populated here -->
            </ul>
        </div>
        <div class="filter-actions">
            <button type="button" class="btn btn-sm btn-secondary filter-cancel">Cancel</button>
            <button type="button" class="btn btn-sm btn-primary filter-apply">Apply Filter</button>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>


    <script src="timestamp-sorting.js?v=37"></script>
    <script src="app.js?v=37"></script>

</body>
</html>
