// Test if JavaScript is loading
console.log("🚀 JavaScript file loaded successfully!");
console.log("🔍 Starting Forensic Timeline Viewer Application...");

// Add global error handler to catch browser extension conflicts
window.addEventListener('error', function(event) {
    // Ignore browser extension errors
    if (event.filename && (
        event.filename.includes('extension://') ||
        event.filename.includes('chrome-extension://') ||
        event.filename.includes('moz-extension://') ||
        event.message.includes('listener indicated an asynchronous response')
    )) {
        console.log('🔇 Ignoring browser extension error:', event.message);
        event.preventDefault();
        return true;
    }

    // Log other errors for debugging
    console.error('🚨 Application error:', event.error || event.message);
});

// Add unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(event) {
    // Ignore browser extension promise rejections
    if (event.reason && event.reason.message &&
        event.reason.message.includes('listener indicated an asynchronous response')) {
        console.log('🔇 Ignoring browser extension promise rejection');
        event.preventDefault();
        return true;
    }

    console.error('🚨 Unhandled promise rejection:', event.reason);
});

// Global variables that need to be available throughout the application
let autoDetectColumns = true;
let eventsTableBody = null;
let eventsTableHead = null;
let currentEvents = [];
let usePagination = true;
let currentPage = 0;
let selectedRowIndex = -1;
let totalEvents = 0;
let paginationPageNum = 1;
let RECORDS_PER_PAGE = 50;
let loadedPages = new Set();
let loadedEvents = {};
let isSearchMode = false;
let currentSearchParams = {
    query: '',
    sort: 'timestamp:asc',
    from: 0,
    size: 50000
};
let columnFilters = {};
let availableColumns = [
    { id: 'timestamp', label: 'Timestamp', visible: true },
    { id: 'event_type', label: 'Event Type', visible: true },
    { id: 'source', label: 'Source', visible: true },
    { id: 'file_name', label: 'File Name', visible: true },
    { id: 'artifact_name', label: 'Artifact Name', visible: true, constant: true },
    { id: 'description', label: 'Description', visible: true },
    { id: 'upload_date', label: 'Upload Date', visible: false }
];

// Function to update window variables dynamically
function updateWindowVariables() {
    window.totalEvents = totalEvents;
    window.paginationPageNum = paginationPageNum;
    window.usePagination = usePagination;
    window.currentEvents = currentEvents;
    window.RECORDS_PER_PAGE = RECORDS_PER_PAGE;
}

// Global error handlers to prevent unhandled promise rejections and async listener errors
window.addEventListener('unhandledrejection', function(event) {
    console.warn('Unhandled promise rejection:', event.reason);
    // Prevent the default behavior (which would log the error to console)
    event.preventDefault();
    // Return false to indicate we handled the rejection
    return false;
});

window.addEventListener('error', function(event) {
    console.warn('Global error caught:', event.error);
    // Don't prevent default behavior for regular errors
    return false;
});

// Additional handler for async listener errors specifically
window.addEventListener('rejectionhandled', function(event) {
    console.warn('Promise rejection handled:', event.reason);
    return false;
});

// Prevent async listener errors by wrapping all async operations
const originalFetch = window.fetch;
window.fetch = function(...args) {
    return originalFetch.apply(this, args).catch(error => {
        console.warn('Fetch error caught and handled:', error);
        throw error; // Re-throw but now it's properly handled
    });
};

// Wrap setTimeout to prevent async listener errors
const originalSetTimeout = window.setTimeout;
window.setTimeout = function(callback, delay, ...args) {
    const wrappedCallback = function() {
        try {
            return callback.apply(this, args);
        } catch (error) {
            console.warn('Timeout callback error caught:', error);
            return false;
        }
    };
    return originalSetTimeout.call(this, wrappedCallback, delay);
};

// Wrap setInterval to prevent async listener errors
const originalSetInterval = window.setInterval;
window.setInterval = function(callback, delay, ...args) {
    const wrappedCallback = function() {
        try {
            return callback.apply(this, args);
        } catch (error) {
            console.warn('Interval callback error caught:', error);
            return false;
        }
    };
    return originalSetInterval.call(this, wrappedCallback, delay);
};

// Comprehensive async operation wrapper to prevent listener errors
function safeAsync(asyncFunction) {
    return function(...args) {
        try {
            const result = asyncFunction.apply(this, args);
            if (result && typeof result.then === 'function') {
                return result.catch(error => {
                    console.warn('Async operation error caught:', error);
                    return null;
                });
            }
            return result;
        } catch (error) {
            console.warn('Sync operation error caught:', error);
            return null;
        }
    };
}

// Wrap addEventListener to ensure no event listener returns true without handling async response
const originalAddEventListener = EventTarget.prototype.addEventListener;
EventTarget.prototype.addEventListener = function(type, listener, options) {
    const wrappedListener = function(event) {
        try {
            const result = listener.call(this, event);
            // If the listener returns true, make sure it's not causing async issues
            if (result === true) {
                console.warn('Event listener returned true, ensuring no async issues');
                return false; // Convert to false to prevent async listener errors
            }
            return result;
        } catch (error) {
            console.warn('Event listener error caught:', error);
            return false;
        }
    };
    return originalAddEventListener.call(this, type, wrappedListener, options);
};

// Early stub functions for functions that are defined later
window.detectColumns = window.detectColumns || function(events) { console.log('detectColumns stub'); };
window.updateTableHeaders = window.updateTableHeaders || function() { console.log('updateTableHeaders stub'); };
window.getEventTypeClass = window.getEventTypeClass || function(eventType) { return 'default'; };
window.getNestedValue = window.getNestedValue || function(obj, path) { return obj[path]; };
window.filterEvents = window.filterEvents || function(events) {
    console.log('filterEvents stub called with', events.length, 'events');
    return events; // Return all events without filtering
};
// Remove stub renderEvents function - will use the proper one defined later

window.showSearchFeedback = window.showSearchFeedback || function(query, totalResults) {
    console.log('showSearchFeedback called:', query, totalResults);

    // Find or create search feedback element
    let searchFeedback = document.getElementById('search-feedback');
    if (!searchFeedback) {
        searchFeedback = document.createElement('div');
        searchFeedback.id = 'search-feedback';
        searchFeedback.className = 'alert mt-3';

        // Insert after the search form
        const searchForm = document.getElementById('search-form');
        if (searchForm && searchForm.parentNode) {
            searchForm.parentNode.insertBefore(searchFeedback, searchForm.nextSibling);
        } else {
            // Fallback if search form not found
            const container = document.querySelector('.container-fluid') || document.body;
            container.insertBefore(searchFeedback, container.firstChild);
        }
    }

    // Format the message based on the number of results
    let resultMessage = '';
    const safeTotal = totalResults || 0;

    if (safeTotal === 0) {
        resultMessage = `<strong>No results found</strong> for: "${query}"`;
        searchFeedback.className = 'alert alert-warning mt-3';
    } else if (safeTotal === 1) {
        resultMessage = `<strong>1 result found</strong> for: "${query}"`;
        searchFeedback.className = 'alert alert-success mt-3';
    } else {
        resultMessage = `<strong>${safeTotal.toLocaleString()} results found</strong> for: "${query}"`;
        searchFeedback.className = 'alert alert-success mt-3';
    }

    searchFeedback.innerHTML = resultMessage;
    searchFeedback.style.display = 'block';
};

// Parse date from various formats
function parseDate(dateStr) {
    if (!dateStr) return null;

    // Try to parse the date
    const date = new Date(dateStr);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
        return null;
    }

    return date;
}

// Format timestamp for display
function formatTimestamp(timestamp) {
    if (!timestamp) return 'N/A';

    // Try to parse the timestamp
    const date = parseDate(timestamp);
    if (!date) return String(timestamp);

    // Format with date and time including milliseconds in UTC+3 timezone
    try {
        // Format the date in UTC+3 timezone
        const options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false,
            timeZone: 'Europe/Moscow' // UTC+3 timezone
        };

        // Format the date with the specified options
        const formattedDate = date.toLocaleString('en-US', options);

        // Add milliseconds
        return formattedDate + '.' + String(date.getMilliseconds()).padStart(3, '0') + ' (UTC+3)';
    } catch (error) {
        // Fallback to standard formatting if timezone formatting fails
        return date.toLocaleString() + '.' + String(date.getMilliseconds()).padStart(3, '0');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    console.log("🎯 DOMContentLoaded event fired - DOM is ready!");
    console.log("📋 Starting to initialize DOM elements...");

    // DOM Elements
    const uploadForm = document.getElementById('upload-form');
    const searchForm = document.getElementById('search-form');
    const searchInput = document.getElementById('search-input');
    const sortField = document.getElementById('sort-field');
    const sortOrder = document.getElementById('sort-order');
    const tableTab = document.getElementById('table-tab');
    const filesTab = document.getElementById('files-tab');
    const tableView = document.getElementById('table-view');
    const filesView = document.getElementById('files-view');
    const eventsTableHead = document.getElementById('events-table-head');
    const eventsTableBodyElement = document.getElementById('events-table-body');

    // Update global variables
    eventsTableBody = eventsTableBodyElement;

    // Update window variables
    updateWindowVariables();
    const filesTableBody = document.getElementById('files-table-body');
    const showingInfo = document.getElementById('showing-info');
    const loadingSpinner = document.getElementById('loading-spinner');
    const loadingMore = document.getElementById('loading-more');
    const eventDetailsModal = new bootstrap.Modal(document.getElementById('event-details-modal'));
    const eventDetailsContent = document.getElementById('event-details-content');
    const eventDetailsPanel = document.getElementById('event-details-panel');
    const detailsPlaceholder = document.getElementById('details-placeholder');
    const closeDetailsBtn = document.getElementById('close-details-btn');
    const fileContentModal = new bootstrap.Modal(document.getElementById('file-content-modal'));
    const fileContentTitle = document.getElementById('file-content-title');
    const fileContentText = document.getElementById('file-content-text');
    const fileContentJson = document.getElementById('file-content-json');
    const fileContentTableHead = document.getElementById('file-content-table-head');
    const fileContentTableBody = document.getElementById('file-content-table-body');
    const viewAsTextBtn = document.getElementById('view-as-text-btn');
    const viewAsJsonBtn = document.getElementById('view-as-json-btn');
    const viewAsTableBtn = document.getElementById('view-as-table-btn');
    const textView = document.getElementById('text-view');
    const jsonView = document.getElementById('json-view');
    const tableViewContainer = document.getElementById('table-view-container');
    const eventsTableContainer = document.getElementById('events-table-container');
    const refreshFilesBtn = document.getElementById('refresh-files-btn');
    const loadSampleBtn = document.getElementById('load-sample-btn');
    const parseLocalFolderBtn = document.getElementById('parse-local-folder-btn');
    const columnSelectBtn = document.getElementById('column-select-btn');
    const columnSelectModal = new bootstrap.Modal(document.getElementById('column-select-modal'));

    // Add event listeners to monitor modal state
    const columnSelectModalElement = document.getElementById('column-select-modal');
    columnSelectModalElement.addEventListener('shown.bs.modal', function() {
        console.log('Column selection modal shown');
    });

    columnSelectModalElement.addEventListener('hidden.bs.modal', function() {
        console.log('Column selection modal hidden');
    });

    columnSelectModalElement.addEventListener('hide.bs.modal', function() {
        console.log('Column selection modal hiding');
    });
    const columnCheckboxes = document.getElementById('column-checkboxes');
    const selectAllColumns = document.getElementById('select-all-columns');
    const detailFieldsSelect = document.getElementById('detail-fields-select');
    const selectedDetailFields = document.getElementById('selected-detail-fields');
    const applyColumnsBtn = document.getElementById('apply-columns-btn');
    const folderSelectModal = new bootstrap.Modal(document.getElementById('folder-select-modal'));
    const folderPath = document.getElementById('folder-path');
    const recursiveCheckbox = document.getElementById('recursive-checkbox');
    const parseFolderBtn = document.getElementById('parse-folder-btn');
    const loadMoreBtn = document.getElementById('load-more-btn');

    // Pagination elements
    const paginationControls = document.getElementById('pagination-controls');
    const paginationToggle = document.getElementById('pagination-toggle');
    const currentPageNum = document.getElementById('current-page-num');
    const totalPagesNum = document.getElementById('total-pages-num');
    const paginationFirst = document.getElementById('pagination-first');
    const paginationPrev = document.getElementById('pagination-prev');
    const paginationNext = document.getElementById('pagination-next');
    const paginationLast = document.getElementById('pagination-last');
    const pageJumpInput = document.getElementById('page-jump-input');
    const pageJumpBtn = document.getElementById('page-jump-btn');

    // Initialize local variables (using global variables where appropriate)
    let pageSize = 50000; // Number of events to load per batch (50k for better performance)
    let isLoading = false;
    let hasMoreEvents = true;
    let allEventsLoaded = false; // Flag to indicate if all events have been loaded
    let maxRetries = 3; // Maximum number of retries for failed requests
    let currentRetries = 0; // Current retry count
    let visibleEvents = []; // Currently visible events
    let totalPaginationPages = 1; // Total number of pages for pagination

    // Search state
    let searchHistory = []; // Array to store search history
    let searchOptions = {
        regex: false,
        caseSensitive: false,
        field: 'all'
    };

    // Update global search parameters
    currentSearchParams.size = pageSize;

    // Local variables for this session
    let detailFields = [];
    let selectedEvent = null;
    let taggedEvents = new Map();

    // Active filter dropdown
    let activeFilterDropdown = null;

    // Define core functions first (before initApp)
    function showView(viewName) {
        console.log(`🔄 Switching to view: ${viewName}`);

        try {
            // Hide all views
            if (tableView) tableView.style.display = 'none';
            if (filesView) filesView.style.display = 'none';
            const taggedView = document.getElementById('tagged-view');
            if (taggedView) taggedView.style.display = 'none';


            // Remove active class from all tabs
            if (tableTab) tableTab.classList.remove('active');
            if (filesTab) filesTab.classList.remove('active');
            const taggedTab = document.getElementById('tagged-tab');
            if (taggedTab) taggedTab.classList.remove('active');


            // Show the selected view and activate the tab
            if (viewName === 'table') {
                if (tableView && tableTab) {
                    tableView.style.display = 'block';
                    tableTab.classList.add('active');
                    console.log("✅ Table view activated");
                } else {
                    console.error("❌ Table view elements not found!");
                }
            } else if (viewName === 'tagged') {
                const taggedView = document.getElementById('tagged-view');
                const taggedTab = document.getElementById('tagged-tab');
                if (taggedView && taggedTab) {
                    taggedView.style.display = 'block';
                    taggedTab.classList.add('active');
                    console.log("✅ Tagged view activated");

                    // Update the tagged events count
                    updateTaggedCount();
                } else {
                    console.error("❌ Tagged view elements not found!");
                }

            } else if (viewName === 'files') {
                if (filesView && filesTab) {
                    filesView.style.display = 'block';
                    filesTab.classList.add('active');
                    console.log("✅ Files view activated");

                    // Load files when switching to this view
                    loadFiles();
                } else {
                    console.error("❌ Files view elements not found!");
                }
            }

        } catch (error) {
            console.error("❌ Error in showView:", error);
        }
    }

    function loadEvents() {
        console.log("loadEvents called - starting to load events");
        console.log("- usePagination:", usePagination);
        console.log("- paginationPageNum:", paginationPageNum);
        console.log("- currentSearchParams:", currentSearchParams);

        // Make sure we're showing the table view
        showView('table');

        // Clear any continuous loading interval
        if (window.continuousLoadingInterval) {
            clearInterval(window.continuousLoadingInterval);
            window.continuousLoadingInterval = null;
        }

        // Initialize the load more button if not already done
        if (!loadMoreBtn) {
            loadMoreBtn = document.getElementById('load-more-btn');
        }

        // If pagination is enabled, use paginated loading
        if (usePagination) {
            console.log("Using pagination mode");
            loadPaginatedEvents();
            return;
        }

        // Otherwise use infinite scroll mode
        console.log("Using infinite scroll mode");

        // Reset state for fresh loading
        resetInfiniteScroll();

        // Start loading from page 0
        loadPage(0, true);
    }

    function loadPaginatedEvents() {
        console.log(`Loading paginated events - page ${paginationPageNum}`);
        console.log("- RECORDS_PER_PAGE:", RECORDS_PER_PAGE);
        console.log("- columnFilters:", columnFilters);

        // Show loading indicator
        try {
            if (typeof showLoading === 'function') {
                showLoading(true);
            }
        } catch (e) {
            console.log('showLoading not available yet');
        }

        // Calculate offset for current page
        const from = (paginationPageNum - 1) * RECORDS_PER_PAGE;
        console.log("- Calculated offset (from):", from);

        // Build query parameters
        const queryParams = new URLSearchParams();
        queryParams.append('from', from);
        queryParams.append('size', RECORDS_PER_PAGE);

        // Add search parameters if in search mode
        if (isSearchMode && currentSearchParams.query) {
            queryParams.append('query', currentSearchParams.query);
            console.log("- Added search query:", currentSearchParams.query);

            // Add search options
            if (currentSearchParams.regex) {
                queryParams.append('regex', 'true');
            }
            if (currentSearchParams.caseSensitive) {
                queryParams.append('caseSensitive', 'true');
            }
            if (currentSearchParams.field && currentSearchParams.field !== 'all') {
                queryParams.append('field', currentSearchParams.field);
            }
        }

        // Add column filters if any
        Object.keys(columnFilters).forEach(column => {
            if (columnFilters[column] && columnFilters[column].length > 0) {
                queryParams.append(`filter_${column}`, columnFilters[column].join(','));
                console.log(`- Added filter: filter_${column} = ${columnFilters[column].join(',')}`);
            }
        });

        // Add cache busting parameter
        queryParams.append('_t', Date.now());

        const apiUrl = `/api/events?${queryParams}`;
        console.log(`Fetching events from: ${apiUrl}`);

        // Make the request with proper error handling
        fetch(apiUrl)
            .then(response => {
                console.log("Fetch response received:", response.status, response.statusText);
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Paginated events response:', data);

                if (data.status) {  // Changed from data.success to data.status
                    // Update total events count
                    totalEvents = data.total || 0;
                    totalPaginationPages = Math.ceil(totalEvents / RECORDS_PER_PAGE);

                    console.log(`Total events: ${totalEvents}, Total pages: ${totalPaginationPages}`);

                    // Store the events
                    currentEvents = data.events || [];
                    console.log(`Loaded ${currentEvents.length} events for page ${paginationPageNum}`);

                    // Process events to use original timestamps
                    currentEvents = currentEvents.map(event => {
                        // If event has details with timestamp, use that as the main timestamp
                        if (event.details) {
                            let details = event.details;
                            if (typeof details === 'string') {
                                try {
                                    details = JSON.parse(details);
                                } catch (e) {
                                    console.warn('Could not parse event details JSON:', e);
                                }
                            }

                            // Check for various timestamp formats in details
                            let originalTimestamp = null;

                            // Check for direct timestamp field
                            if (details && details.timestamp) {
                                originalTimestamp = details.timestamp;
                            }
                            // Check for SystemTime in System.TimeCreated (Windows Event Log format)
                            else if (details && details.System && details.System.TimeCreated && details.System.TimeCreated.SystemTime) {
                                // Convert Unix timestamp to ISO string
                                const unixTimestamp = details.System.TimeCreated.SystemTime;
                                originalTimestamp = new Date(unixTimestamp * 1000).toISOString();
                            }
                            // Check for other common timestamp fields
                            else if (details && details.time) {
                                originalTimestamp = details.time;
                            }
                            else if (details && details.datetime) {
                                originalTimestamp = details.datetime;
                            }

                            // Use the original timestamp if found
                            if (originalTimestamp) {
                                event.timestamp = originalTimestamp;
                                console.log(`Updated timestamp for event ${event.id}: ${originalTimestamp}`);
                            }
                        }
                        return event;
                    });

                    // Apply any client-side filtering if needed
                    const eventsToRender = filterEvents(currentEvents);

                    // Render the events
                    renderEvents(eventsToRender, true);

                    // Update showing info
                    updateShowingInfo();



                    // Show search feedback if in search mode
                    if (isSearchMode && currentSearchParams.query) {
                        showSearchFeedback(currentSearchParams.query, totalEvents);
                    }

                    // If we loaded a large batch but only showing a subset, inform the user
                    if (currentEvents.length > RECORDS_PER_PAGE && !isSearchMode) {
                        console.log(`Loaded ${currentEvents.length} events, displaying first ${RECORDS_PER_PAGE}`);
                    }
                } else {
                    console.error('Error loading events:', data.message);
                    try {
                        if (typeof showError === 'function') {
                            showError(`Error loading events: ${data.message}`);
                        } else {
                            console.error('showError function not available, error:', data.message);
                        }
                    } catch (e) {
                        console.error('Error calling showError:', e);
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching events:', error);
                try {
                    if (typeof showError === 'function') {
                        showError(`Error fetching events: ${error.message}`);
                    } else {
                        console.error('showError function not available, error:', error.message);
                    }
                } catch (e) {
                    console.error('Error calling showError:', e);
                }
            })
            .finally(() => {
                // Hide loading indicator
                try {
                    if (typeof showLoading === 'function') {
                        showLoading(false);
                    }
                } catch (e) {
                    console.log('showLoading not available yet');
                }

                // Update pagination controls
                try {
                    if (typeof updatePaginationControls === 'function') {
                        updatePaginationControls();
                    }
                } catch (e) {
                    console.log('updatePaginationControls not available yet');
                }
            });
    }

    function updateShowingInfo() {
        const showingText = document.querySelector('#showing-info .d-flex .text-white');
        if (showingText) {
            if (usePagination) {
                const startRecord = (paginationPageNum - 1) * RECORDS_PER_PAGE + 1;
                const endRecord = Math.min(paginationPageNum * RECORDS_PER_PAGE, totalEvents);
                showingText.textContent = `Showing ${startRecord.toLocaleString()}-${endRecord.toLocaleString()} of ${totalEvents.toLocaleString()} events`;
            } else {
                const totalLoaded = Array.from(loadedPages).reduce((total, p) => total + (loadedEvents[p]?.length || 0), 0);
                showingText.textContent = `Showing ${totalLoaded.toLocaleString()} of ${totalEvents.toLocaleString()} events`;
            }
        }
    }



    // Initialize the application
    initApp();

    function initApp() {
        console.log("initApp() called - starting application initialization");

        // Check if critical DOM elements exist
        console.log("Checking DOM elements:");
        console.log("- uploadForm:", uploadForm ? "found" : "NOT FOUND");
        console.log("- searchForm:", searchForm ? "found" : "NOT FOUND");
        console.log("- eventsTableBody:", eventsTableBody ? "found" : "NOT FOUND");
        console.log("- paginationToggle:", paginationToggle ? "found" : "NOT FOUND");
        console.log("- paginationControls:", paginationControls ? "found" : "NOT FOUND");
        console.log("- tableTab:", tableTab ? "found" : "NOT FOUND");
        console.log("- filesTab:", filesTab ? "found" : "NOT FOUND");
        console.log("- loadSampleBtn:", loadSampleBtn ? "found" : "NOT FOUND");
        console.log("- refreshFilesBtn:", refreshFilesBtn ? "found" : "NOT FOUND");

        // Load saved column configuration (if function is available)
        try {
            if (typeof loadColumnConfiguration === 'function') {
                loadColumnConfiguration();
            }
        } catch (error) {
            console.log('Column configuration will be loaded later');
        }

        // Ensure pagination is enabled by default
        usePagination = true;
        if (paginationToggle) {
            paginationToggle.checked = true;
            console.log("Pagination toggle set to checked");
        } else {
            console.error("Pagination toggle element not found!");
        }
        if (paginationControls) {
            paginationControls.style.display = 'block';
            console.log("Pagination controls shown");
        } else {
            console.error("Pagination controls element not found!");
        }

        // Set up event listeners
        console.log("Setting up event listeners...");
        setupEventListeners();

        // Check data status first, then load initial data
        console.log("Checking data status...");
        checkDataStatus().then(() => {
            console.log("Loading initial events...");
            loadEvents();
        }).catch(error => {
            console.error("Error checking data status:", error);
            // Still try to load events even if status check fails
            console.log("Loading initial events anyway...");
            loadEvents();
        });

        // Show table view by default
        console.log("Showing table view...");
        showView('table');

        console.log("initApp() completed");
    }

    // Function to check if data already exists
    async function checkDataStatus() {
        try {
            const response = await fetch('/api/data-status');
            const data = await response.json();

            if (data.status && data.hasData) {
                console.log(`Found existing data: ${data.totalEvents} events from ${data.files.length} files`);
                console.log('Files:', data.files);

                // Update total events count
                totalEvents = data.totalEvents;

                // Show data status in UI
                showDataStatus(data);

                return data;
            } else {
                console.log('No existing data found');
                return null;
            }
        } catch (error) {
            console.error('Error checking data status:', error);
            return null;
        }
    }

    // Function to show data status in UI
    function showDataStatus(data) {
        const statusDiv = document.createElement('div');
        statusDiv.className = 'alert alert-info mt-2';
        statusDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>Existing Data Found:</strong> ${data.totalEvents.toLocaleString()} events from ${data.files.length} files
                    <small class="d-block text-muted">Storage: ${data.storageType.toUpperCase()}</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="this.parentElement.parentElement.remove()">
                    <i class="bi bi-x"></i>
                </button>
            </div>
        `;

        // Insert after the upload form
        const uploadCard = document.querySelector('.card');
        if (uploadCard) {
            uploadCard.insertAdjacentElement('afterend', statusDiv);
        }
    }

    function setupEventListeners() {
        console.log("🔧 Setting up event listeners...");

        // Date search button
        const dateSearchBtn = document.getElementById('date-search-btn');
        const dateSearchPanel = document.getElementById('date-search-panel');
        const dateSearchCancel = document.getElementById('date-search-cancel');
        const dateSearchApply = document.getElementById('date-search-apply');
        const dateSearchStart = document.getElementById('date-search-start');
        const dateSearchEnd = document.getElementById('date-search-end');

        console.log("Date search elements:", {
            dateSearchBtn: !!dateSearchBtn,
            dateSearchPanel: !!dateSearchPanel,
            dateSearchCancel: !!dateSearchCancel,
            dateSearchApply: !!dateSearchApply
        });

        // Time input elements
        const startHour = document.getElementById('start-hour');
        const startMinute = document.getElementById('start-minute');
        const startSecond = document.getElementById('start-second');
        const startMillisecond = document.getElementById('start-millisecond');
        const endHour = document.getElementById('end-hour');
        const endMinute = document.getElementById('end-minute');
        const endSecond = document.getElementById('end-second');
        const endMillisecond = document.getElementById('end-millisecond');

        // Preset buttons
        const presetToday = document.getElementById('preset-today');
        const presetYesterday = document.getElementById('preset-yesterday');
        const presetLast7Days = document.getElementById('preset-last-7-days');
        const presetLast30Days = document.getElementById('preset-last-30-days');
        const setCurrentTimeBtn = document.getElementById('set-current-time');

        // Show date search panel
        if (dateSearchBtn) {
            dateSearchBtn.addEventListener('click', function() {
            dateSearchPanel.style.display = 'block';

            // Set default dates if not already set
            if (!dateSearchStart.value) {
                // Default to 7 days ago
                const sevenDaysAgo = new Date();
                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                dateSearchStart.value = sevenDaysAgo.toISOString().split('T')[0];
            }

            if (!dateSearchEnd.value) {
                // Default to today
                const today = new Date();
                dateSearchEnd.value = today.toISOString().split('T')[0];
            }
        });

        // Cancel date search
        if (dateSearchCancel) {
            dateSearchCancel.addEventListener('click', function() {
                dateSearchPanel.style.display = 'none';
            });
        }

        // Set current time button
        if (setCurrentTimeBtn) {
            setCurrentTimeBtn.addEventListener('click', function() {
                const now = new Date();
                endHour.value = now.getHours();
                endMinute.value = now.getMinutes();
                endSecond.value = now.getSeconds();
                endMillisecond.value = now.getMilliseconds();
            });
        }

        // Preset button handlers
        if (presetToday) {
            presetToday.addEventListener('click', function() {
            const today = new Date();
            const todayStr = today.toISOString().split('T')[0];
            dateSearchStart.value = todayStr;
            dateSearchEnd.value = todayStr;
            // Clear time fields for full day
            clearTimeFields();
        });
        }

        if (presetYesterday) {
            presetYesterday.addEventListener('click', function() {
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            const yesterdayStr = yesterday.toISOString().split('T')[0];
            dateSearchStart.value = yesterdayStr;
            dateSearchEnd.value = yesterdayStr;
            // Clear time fields for full day
            clearTimeFields();
        });
        }

        if (presetLast7Days) {
            presetLast7Days.addEventListener('click', function() {
            const today = new Date();
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(today.getDate() - 7);
            dateSearchStart.value = sevenDaysAgo.toISOString().split('T')[0];
            dateSearchEnd.value = today.toISOString().split('T')[0];
            // Clear time fields for full day range
            clearTimeFields();
        });
        }

        if (presetLast30Days) {
            presetLast30Days.addEventListener('click', function() {
            const today = new Date();
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(today.getDate() - 30);
            dateSearchStart.value = thirtyDaysAgo.toISOString().split('T')[0];
            dateSearchEnd.value = today.toISOString().split('T')[0];
            // Clear time fields for full day range
            clearTimeFields();
        });
        }

        function clearTimeFields() {
            startHour.value = '';
            startMinute.value = '';
            startSecond.value = '';
            startMillisecond.value = '';
            endHour.value = '';
            endMinute.value = '';
            endSecond.value = '';
            endMillisecond.value = '';
        }

        // Apply date filter
        if (dateSearchApply) {
            dateSearchApply.addEventListener('click', function() {
            const startDate = dateSearchStart.value;
            const endDate = dateSearchEnd.value;

            if (!startDate || !endDate) {
                alert('Please select both start and end dates');
                return;
            }

            // Build start and end datetime strings
            let startDateTime = startDate;
            let endDateTime = endDate;

            // Add time components if specified
            if (startHour.value || startMinute.value || startSecond.value || startMillisecond.value) {
                const hour = startHour.value || '00';
                const minute = startMinute.value || '00';
                const second = startSecond.value || '00';
                const ms = startMillisecond.value || '000';
                startDateTime += `T${hour.padStart(2, '0')}:${minute.padStart(2, '0')}:${second.padStart(2, '0')}.${ms.padStart(3, '0')}Z`;
            } else {
                startDateTime += 'T00:00:00.000Z';
            }

            if (endHour.value || endMinute.value || endSecond.value || endMillisecond.value) {
                const hour = endHour.value || '23';
                const minute = endMinute.value || '59';
                const second = endSecond.value || '59';
                const ms = endMillisecond.value || '999';
                endDateTime += `T${hour.padStart(2, '0')}:${minute.padStart(2, '0')}:${second.padStart(2, '0')}.${ms.padStart(3, '0')}Z`;
            } else {
                endDateTime += 'T23:59:59.999Z';
            }

            // Hide the panel
            dateSearchPanel.style.display = 'none';

            // Enter search mode
            isSearchMode = true;

            // Create a date range query
            const dateQuery = `timestamp:[${startDateTime} TO ${endDateTime}]`;

            // Update search input to show what we're searching for
            const displayQuery = startDateTime === startDate + 'T00:00:00.000Z' && endDateTime === endDate + 'T23:59:59.999Z'
                ? `Date: ${startDate} to ${endDate}`
                : `DateTime: ${startDateTime} to ${endDateTime}`;
            document.getElementById('search-input').value = displayQuery;

            // Show loading indicator
            showLoadingIndicator(`Searching for events between ${startDateTime} and ${endDateTime}...`);

            // Update search parameters
            currentSearchParams = {
                query: dateQuery,
                searchOptions: {
                    regex: false,
                    caseSensitive: false,
                    field: 'timestamp'
                },
                sort: `${sortField.value}:${sortOrder.value}`
            };

            // Reset pagination and loaded events
            resetInfiniteScroll();

            // Force pagination mode when searching
            const originalPaginationMode = usePagination;
            usePagination = true;

            // Load events with the date filter
            loadEvents();

            // Create a search mode indicator if it doesn't exist
            let searchModeIndicator = document.getElementById('search-mode-indicator');
            if (!searchModeIndicator) {
                searchModeIndicator = document.createElement('div');
                searchModeIndicator.id = 'search-mode-indicator';
                searchModeIndicator.className = 'badge bg-primary position-fixed top-0 end-0 m-2';
                searchModeIndicator.style.zIndex = '1050';
                document.body.appendChild(searchModeIndicator);
            }

            searchModeIndicator.style.display = 'inline-block';
            searchModeIndicator.textContent = `Date Filter: ${startDate} to ${endDate}`;
        });
        }

        // Upload form submission - direct approach
        if (uploadForm) {
            console.log("Setting up upload form event listener");

            uploadForm.onsubmit = function(e) {
                e.preventDefault();
                console.log("Upload form submitted directly");
                uploadFile();
                return false;
            };

            // Also add a direct click handler to the upload button for redundancy
            const uploadButton = uploadForm.querySelector('button[type="submit"]');
            if (uploadButton) {
                console.log("Setting up upload button click handler");

                uploadButton.onclick = function(e) {
                    e.preventDefault();
                    console.log("Upload button clicked directly");
                    uploadFile();
                    return false;
                };
            }
        } else {
            console.error("Upload form not found in the DOM");
        }

        // Load sample data
        if (loadSampleBtn) {
            console.log("✅ Setting up load sample button listener");
            loadSampleBtn.addEventListener('click', function() {
                console.log("🔄 Load sample button clicked");
                loadSampleData();
            });
        } else {
            console.error("❌ Load sample button not found!");
        }

        // Parse local folder
        if (parseLocalFolderBtn) {
            parseLocalFolderBtn.addEventListener('click', function() {
                if (folderSelectModal) {
                    folderSelectModal.show();
                }
            });
        }

        // Parse folder button in modal
        if (parseFolderBtn) {
            parseFolderBtn.addEventListener('click', function() {
                const path = folderPath ? folderPath.value.trim() : '';
                if (path) {
                    parseLocalFolder(path, recursiveCheckbox ? recursiveCheckbox.checked : false);
                    if (folderSelectModal) {
                        folderSelectModal.hide();
                    }
                } else {
                    alert('Please enter a folder path');
                }
            });
        }

        // Pagination toggle
        paginationToggle.addEventListener('change', function() {
            usePagination = this.checked;

            // Update UI based on pagination mode
            if (usePagination) {
                paginationControls.style.display = 'block';
                // Reset to page 1 and reload events
                paginationPageNum = 1;
                loadPaginatedEvents();
            } else {
                paginationControls.style.display = 'none';
                // Switch back to infinite scroll mode
                resetInfiniteScroll();
                loadEvents();
            }
        });

        // Pagination controls
        paginationFirst.addEventListener('click', function(e) {
            e.preventDefault();
            if (!paginationFirst.classList.contains('disabled')) {
                navigateToPage(1);
            }
        });

        paginationPrev.addEventListener('click', function(e) {
            e.preventDefault();
            if (!paginationPrev.classList.contains('disabled')) {
                navigateToPage(paginationPageNum - 1);
            }
        });

        paginationNext.addEventListener('click', function(e) {
            e.preventDefault();
            if (!paginationNext.classList.contains('disabled')) {
                navigateToPage(paginationPageNum + 1);
            }
        });

        paginationLast.addEventListener('click', function(e) {
            e.preventDefault();
            if (!paginationLast.classList.contains('disabled')) {
                navigateToPage(totalPaginationPages);
            }
        });

        // Page jump
        pageJumpBtn.addEventListener('click', function() {
            const pageNum = parseInt(pageJumpInput.value);
            if (pageNum && pageNum > 0 && pageNum <= totalPaginationPages) {
                navigateToPage(pageNum);
            } else {
                alert(`Please enter a valid page number between 1 and ${totalPaginationPages}`);
                pageJumpInput.value = paginationPageNum;
            }
        });

        // Allow Enter key in page jump input
        pageJumpInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                pageJumpBtn.click();
            }
        });

        // Erase all data
        const eraseAllDataBtn = document.getElementById('erase-all-data-btn');
        eraseAllDataBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to erase ALL data and delete ALL uploaded files?\n\nThis will:\n• Delete all events from the database\n• Remove all uploaded files from the server\n• Clear all temporary files\n\nThis action cannot be undone!')) {
                eraseAllData();
            }
        });

        // Column selection
        columnSelectBtn.addEventListener('click', function() {
            console.log('Column select button clicked');
            console.log('Available columns:', availableColumns);

            // Always ensure we have at least the basic columns
            const basicColumns = [
                { id: 'timestamp', label: 'Timestamp', visible: true },
                { id: 'event_type', label: 'Event Type', visible: true },
                { id: 'source', label: 'Source', visible: true },
                { id: 'file_name', label: 'File Name', visible: true },
                { id: 'artifact_name', label: 'Artifact Name', visible: true, constant: true },
                { id: 'description', label: 'Description', visible: true },
                { id: 'upload_date', label: 'Upload Date', visible: false }
            ];

            // If we have no columns or only basic columns, ensure we have the basic set
            if (availableColumns.length === 0) {
                availableColumns = [...basicColumns];
            } else {
                // Ensure all basic columns exist
                basicColumns.forEach(basicCol => {
                    const exists = availableColumns.find(col => col.id === basicCol.id);
                    if (!exists) {
                        availableColumns.push(basicCol);
                    }
                });
            }

            // Try to detect additional columns from current events BEFORE populating modal
            if (currentEvents && currentEvents.length > 0) {
                console.log('Detecting columns from current events before opening modal');
                detectColumns(currentEvents.slice(0, 10));
            }

            // Now populate and show the modal
            console.log('Populating modal with columns:', availableColumns.length);
            try {
                populateColumnSelectionModal();

                // Verify that the modal was properly populated
                const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]');
                console.log('Modal populated with', checkboxes.length, 'checkboxes');

                if (checkboxes.length > 0) {
                    columnSelectModal.show();
                    console.log('Column selection modal opened successfully');
                } else {
                    console.error('Modal population failed - no checkboxes found');
                    alert('Error: Could not populate column selection modal. Please try again.');
                }
            } catch (error) {
                console.error('Error populating column selection modal:', error);
                alert('Error opening column selection modal. Please try again.');
            }
        });

        // Select all columns checkbox
        selectAllColumns.addEventListener('change', function() {
            const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllColumns.checked;
            });
        });

        // Apply column selection
        applyColumnsBtn.addEventListener('click', function() {
            console.log('Apply columns button clicked');

            // FIRST: Force close the modal immediately
            try {
                columnSelectModal.hide();
                console.log('Modal hide() called');
            } catch (error) {
                console.error('Error hiding modal:', error);
                // Force close using DOM manipulation if Bootstrap method fails
                forceCloseModal();
            }

            // Force close using DOM manipulation as well to be sure
            forceCloseModal();

            // THEN: Apply column selection after a short delay to ensure modal is closed
            setTimeout(() => {
                console.log('Applying column selection after modal close');
                applyColumnSelection();
            }, 100);

            // Double-check that modal is actually closed after longer delay
            setTimeout(() => {
                console.log('Final check - column selection modal should now be closed');
                const modalElement = document.getElementById('column-select-modal');
                if (modalElement && modalElement.classList.contains('show')) {
                    console.warn('Modal still showing after final check, forcing close again');
                    forceCloseModal();
                }
            }, 1000);
        });

        // Force close modal function - make it globally accessible
        window.forceCloseModal = function() {
            console.log('Force closing modal');
            const modalElement = document.getElementById('column-select-modal');
            if (modalElement) {
                modalElement.classList.remove('show');
                modalElement.style.display = 'none';
                modalElement.setAttribute('aria-hidden', 'true');
                modalElement.removeAttribute('aria-modal');

                // Remove backdrop if it exists
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }

                // Remove modal-open class from body
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';

                console.log('Modal force closed');
            }
        };

        // Local reference for convenience
        const forceCloseModal = window.forceCloseModal;

        // Add detail field
        detailFieldsSelect.addEventListener('change', function() {
            const field = detailFieldsSelect.value;
            if (field) {
                addDetailField(field);
                detailFieldsSelect.value = '';
            }
        });

        // Initialize search options
        const regexSearchCheckbox = document.getElementById('regex-search-checkbox');
        const caseSensitiveCheckbox = document.getElementById('case-sensitive-checkbox');
        const searchFieldSelect = document.getElementById('search-field');

        // Search options change handlers
        regexSearchCheckbox.addEventListener('change', function() {
            searchOptions.regex = this.checked;
        });

        caseSensitiveCheckbox.addEventListener('change', function() {
            searchOptions.caseSensitive = this.checked;
        });

        searchFieldSelect.addEventListener('change', function() {
            searchOptions.field = this.value;
        });

        // Search form submission
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Update search parameters
            updateSearchParams();

            // Add to search history if not empty and not already in history
            const searchQuery = searchInput.value.trim();
            if (searchQuery && !searchHistory.includes(searchQuery)) {
                searchHistory.unshift(searchQuery); // Add to beginning of array
                if (searchHistory.length > 10) {
                    searchHistory.pop(); // Keep only the 10 most recent searches
                }
                updateSearchHistory();
            }

            // If query is empty, exit search mode
            if (!searchQuery) {
                isSearchMode = false;
                clearSearchFeedback();

                // Create a search mode indicator if it doesn't exist
                let searchModeIndicator = document.getElementById('search-mode-indicator');
                if (searchModeIndicator) {
                    searchModeIndicator.style.display = 'none';
                }
            } else {
                // Enter search mode - this will search across all pages
                isSearchMode = true;

                // Show initial search feedback (will be updated with result count after loading)
                showSearchFeedback(searchQuery, null);

                // Create a search mode indicator if it doesn't exist
                let searchModeIndicator = document.getElementById('search-mode-indicator');
                if (!searchModeIndicator) {
                    searchModeIndicator = document.createElement('div');
                    searchModeIndicator.id = 'search-mode-indicator';
                    searchModeIndicator.className = 'badge bg-primary position-fixed top-0 end-0 m-2';
                    searchModeIndicator.style.zIndex = '1050';
                    document.body.appendChild(searchModeIndicator);
                }

                searchModeIndicator.style.display = 'inline-block';
                searchModeIndicator.textContent = `Search Mode: "${searchQuery}"`;
            }

            // Make sure the search button is always visible
            const searchButton = searchForm.querySelector('button[type="submit"]');
            if (searchButton) {
                searchButton.style.display = 'block';
            }

            // Reset to first page when searching
            paginationPageNum = 1;

            // Load events with search parameters - always use loadEvents() for consistency
            console.log('🔍 Starting search with parameters:', currentSearchParams);
            loadEvents();
        });

        // Clear search button
        const clearSearchBtn = document.createElement('button');
        clearSearchBtn.type = 'button';
        clearSearchBtn.className = 'btn btn-outline-secondary';
        clearSearchBtn.innerHTML = '<i class="bi bi-x-circle"></i>';
        clearSearchBtn.style.display = 'none';
        clearSearchBtn.title = 'Clear search';
        clearSearchBtn.addEventListener('click', function() {
            searchInput.value = '';
            clearSearchBtn.style.display = 'none';
            clearSearchFeedback();
            resetInfiniteScroll();

            // Reset search options
            regexSearchCheckbox.checked = false;
            caseSensitiveCheckbox.checked = false;
            searchFieldSelect.value = 'all';
            searchOptions.regex = false;
            searchOptions.caseSensitive = false;
            searchOptions.field = 'all';

            // Exit search mode
            isSearchMode = false;

            // Hide search mode indicator
            const searchModeIndicator = document.getElementById('search-mode-indicator');
            if (searchModeIndicator) {
                searchModeIndicator.style.display = 'none';
            }

            // Reset search parameters
            currentSearchParams = {
                query: '',
                searchOptions: {
                    regex: false,
                    caseSensitive: false,
                    field: 'all'
                },
                sort: `${sortField.value}:${sortOrder.value}`
            };

            // Reset to first page when clearing search
            paginationPageNum = 1;

            if (usePagination) {
                loadPaginatedEvents();
            } else {
                loadEvents();
            }
        });

        // Add clear button after search input
        searchInput.parentNode.appendChild(clearSearchBtn);

        // Show/hide clear button based on search input
        searchInput.addEventListener('input', function() {
            clearSearchBtn.style.display = searchInput.value.trim() ? 'block' : 'none';
        });

        // Tab switching with error handling
        if (tableTab) {
            console.log("✅ Setting up table tab listener");
            tableTab.addEventListener('click', function(e) {
                e.preventDefault();
                console.log("🔄 Table tab clicked");
                showView('table');
            });
        } else {
            console.error("❌ Table tab element not found!");
        }

        const taggedTab = document.getElementById('tagged-tab');
        if (taggedTab) {
            console.log("✅ Setting up tagged tab listener");
            taggedTab.addEventListener('click', function(e) {
                e.preventDefault();
                console.log("🔄 Tagged tab clicked");
                showView('tagged');
                renderTaggedEvents();
            });
        } else {
            console.error("❌ Tagged tab element not found!");
        }



        if (filesTab) {
            console.log("✅ Setting up files tab listener");
            filesTab.addEventListener('click', function(e) {
                e.preventDefault();
                console.log("🔄 Files tab clicked");
                try {
                    showView('files');
                    loadFiles();
                } catch (error) {
                    console.error('Error in files tab click handler:', error);
                }
                return false; // Explicitly return false to prevent async listener issues
            });
        } else {
            console.error("❌ Files tab element not found!");
        }

        // Refresh files list
        if (refreshFilesBtn) {
            console.log("✅ Setting up refresh files button listener");
            refreshFilesBtn.addEventListener('click', function() {
                console.log("🔄 Refresh files button clicked");
                try {
                    loadFiles();
                } catch (error) {
                    console.error('Error in refresh files click handler:', error);
                }
                return false; // Explicitly return false to prevent async listener issues
            });
        } else {
            console.error("❌ Refresh files button not found!");
        }

        // File content view buttons
        viewAsTextBtn.addEventListener('click', function() {
            textView.style.display = 'block';
            jsonView.style.display = 'none';
            tableViewContainer.style.display = 'none';

            viewAsTextBtn.classList.add('active');
            viewAsJsonBtn.classList.remove('active');
            viewAsTableBtn.classList.remove('active');
        });

        viewAsJsonBtn.addEventListener('click', function() {
            textView.style.display = 'none';
            jsonView.style.display = 'block';
            tableViewContainer.style.display = 'none';

            viewAsTextBtn.classList.remove('active');
            viewAsJsonBtn.classList.add('active');
            viewAsTableBtn.classList.remove('active');
        });

        viewAsTableBtn.addEventListener('click', function() {
            textView.style.display = 'none';
            jsonView.style.display = 'none';
            tableViewContainer.style.display = 'block';

            viewAsTextBtn.classList.remove('active');
            viewAsJsonBtn.classList.remove('active');
            viewAsTableBtn.classList.add('active');
        });

        // Close details panel
        closeDetailsBtn.addEventListener('click', function() {
            hideEventDetails();
        });

        // Virtual scrolling
        window.addEventListener('scroll', function() {
            if (tableView.style.display !== 'none') {
                const scrollPosition = window.innerHeight + window.scrollY;
                const bodyHeight = document.body.offsetHeight;

                // Load more when user scrolls to 70% of the page (more aggressive)
                if (scrollPosition >= bodyHeight * 0.7 && !isLoading && hasMoreEvents) {
                    loadMoreEvents();
                }

                // Check if we need to load more pages
                checkVisibleRows();

                // Also periodically check if we should load more events
                // This helps ensure continuous loading even if the user isn't scrolling
                if (hasMoreEvents && !window.continuousLoadingInterval) {
                    window.continuousLoadingInterval = setInterval(() => {
                        if (!isLoading && hasMoreEvents) {
                            const totalLoaded = Array.from(loadedPages).reduce((total, p) => total + (loadedEvents[p]?.length || 0), 0);
                            const percentLoaded = Math.round((totalLoaded / totalEvents) * 100);

                            // If we've loaded less than 50% of the data, keep loading more
                            if (percentLoaded < 50) {
                                console.log(`Continuous loading: ${percentLoaded}% loaded so far`);
                                loadMoreEvents();
                            } else {
                                // Once we've loaded enough, clear the interval
                                clearInterval(window.continuousLoadingInterval);
                                window.continuousLoadingInterval = null;
                            }
                        }
                    }, 3000); // Check every 3 seconds
                }
            }
        });



        // Sort field change
        sortField.addEventListener('change', function() {
            console.log('Sort field changed to:', this.value);
            currentSearchParams.sort = `${this.value}:${sortOrder.value}`;

            // Reset to first page when sorting
            paginationPageNum = 1;

            if (usePagination) {
                loadPaginatedEvents();
            } else {
                resetInfiniteScroll();
                loadEvents();
            }
        });

        // Sort order change
        sortOrder.addEventListener('change', function() {
            console.log('Sort order changed to:', this.value);
            currentSearchParams.sort = `${sortField.value}:${this.value}`;

            // Reset to first page when sorting
            paginationPageNum = 1;

            if (usePagination) {
                loadPaginatedEvents();
            } else {
                resetInfiniteScroll();
                loadEvents();
            }
        });

        // Close filter dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (activeFilterDropdown && !activeFilterDropdown.contains(e.target)) {
                closeFilterDropdown();
            }
        });

        // Add keyboard navigation and tagging
        document.addEventListener('keydown', function(e) {
            // Only handle keyboard navigation when table view is active
            if (tableView.style.display !== 'none') {
                const rows = eventsTableBody.querySelectorAll('tr.event-row');
                if (rows.length === 0) return;

                // Handle arrow keys
                if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                    e.preventDefault(); // Prevent default scrolling

                    // Calculate new index
                    if (e.key === 'ArrowDown') {
                        // Move down
                        selectedRowIndex = (selectedRowIndex < rows.length - 1) ? selectedRowIndex + 1 : selectedRowIndex;
                    } else {
                        // Move up
                        selectedRowIndex = (selectedRowIndex > 0) ? selectedRowIndex - 1 : 0;
                    }

                    // Get the selected row
                    const selectedRow = rows[selectedRowIndex];
                    if (selectedRow) {
                        // Get the event ID from the row
                        const eventId = selectedRow.dataset.eventId;

                        // Find the event in loaded events
                        let foundEvent = null;
                        for (const page in loadedEvents) {
                            const pageEvents = loadedEvents[page];
                            foundEvent = pageEvents.find(event => event.id === eventId);
                            if (foundEvent) break;
                        }

                        if (foundEvent) {
                            // Highlight the row and show details
                            rows.forEach(row => {
                                row.classList.remove('table-primary');
                                row.classList.remove('keyboard-focus');
                            });
                            selectedRow.classList.add('table-primary');
                            selectedRow.classList.add('keyboard-focus');

                            // Ensure the row is visible with improved autoscroll
                            console.log('🎯 Autoscrolling to selected row');
                            selectedRow.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center',
                                inline: 'nearest'
                            });

                            // Add a brief highlight effect to make the selection more obvious
                            selectedRow.style.transition = 'all 0.3s ease';
                            selectedRow.style.transform = 'scale(1.02)';
                            setTimeout(() => {
                                selectedRow.style.transform = 'scale(1)';
                            }, 300);

                            // Show event details
                            showEventDetailsInPanel(foundEvent);
                        }
                    }
                }

                // Handle 'm' key for tagging events
                if (e.key === 'm' || e.key === 'M') {
                    e.preventDefault();

                    // Get the currently selected row
                    const selectedRow = rows[selectedRowIndex];
                    if (selectedRow) {
                        // Get the event ID from the row
                        const eventId = selectedRow.dataset.eventId;

                        // Find the event in loaded events
                        let foundEvent = null;
                        for (const page in loadedEvents) {
                            const pageEvents = loadedEvents[page];
                            foundEvent = pageEvents.find(event => event.id === eventId);
                            if (foundEvent) break;
                        }

                        if (foundEvent) {
                            // Toggle tag for this event
                            toggleEventTag(foundEvent, selectedRow);
                        }
                    }
                }
            }
        });

        // Add clear tags button handler
        const clearTagsBtn = document.getElementById('clear-tags-btn');
        if (clearTagsBtn) {
            console.log("✅ Setting up clear tags button listener");
            clearTagsBtn.addEventListener('click', function() {
                console.log("🔄 Clear tags button clicked");
                if (confirm('Are you sure you want to clear all tagged events?')) {
                    clearAllTags();
                }
            });
        } else {
            console.error("❌ Clear tags button not found!");
        }

        console.log("✅ Event listeners setup completed successfully!");
    }

    function updateSearchParams() {
        let query = searchInput.value.trim();
        console.log('🔍 updateSearchParams called with query:', query);

        // Format query based on search options
        if (query) {
            // If regex is enabled and the query isn't already in regex format
            if (searchOptions.regex && !query.match(/^\/.*\/$/)) {
                query = `/${query}/`;
                console.log('🔍 Formatted query for regex:', query);
            }
        }

        // Update global search parameters with proper API format
        currentSearchParams = {
            query: query,
            sort: `${sortField.value}:${sortOrder.value}`,
            from: 0,
            size: usePagination ? RECORDS_PER_PAGE : pageSize,
            regex: searchOptions.regex,
            caseSensitive: searchOptions.caseSensitive,
            field: searchOptions.field
        };

        console.log('🔍 Updated search params:', currentSearchParams);
    }

    // Function to update search history datalist
    function updateSearchHistory() {
        const searchHistoryDatalist = document.getElementById('search-history');
        searchHistoryDatalist.innerHTML = '';

        // Add each search term to the datalist
        searchHistory.forEach(term => {
            const option = document.createElement('option');
            option.value = term;
            searchHistoryDatalist.appendChild(option);
        });
    }

    // Function to show search feedback
    function showSearchFeedback(query, totalResults) {
        // Create or update search feedback element
        let searchFeedback = document.getElementById('search-feedback');
        if (!searchFeedback) {
            searchFeedback = document.createElement('div');
            searchFeedback.id = 'search-feedback';
            searchFeedback.className = 'alert alert-info d-flex align-items-center mt-2';

            // Insert after the search form
            const searchForm = document.getElementById('search-form');
            if (searchForm && searchForm.parentNode) {
                searchForm.parentNode.insertBefore(searchFeedback, searchForm.nextSibling);
            } else {
                // Fallback if search form not found
                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(searchFeedback, container.firstChild);
                console.log('Using fallback container for search feedback');
            }
        }

        // Build search options badges
        let optionsBadges = '';

        if (searchOptions.regex) {
            optionsBadges += `<span class="badge bg-secondary me-1">Regex</span>`;
        }

        if (searchOptions.caseSensitive) {
            optionsBadges += `<span class="badge bg-secondary me-1">Case Sensitive</span>`;
        }

        if (searchOptions.field !== 'all') {
            optionsBadges += `<span class="badge bg-secondary me-1">Field: ${searchOptions.field}</span>`;
        }

        // Add options badges if any
        const optionsDisplay = optionsBadges ?
            `<div class="mt-1">${optionsBadges}</div>` : '';

        // Format the message based on the number of results
        let resultMessage = '';
        let alertClass = 'alert-info';
        let searchProgressPercent = 0;
        let searchProgressMessage = '';
        let isSearchComplete = false;

        if (totalResults === null) {
            // Initial state when search is just submitted
            resultMessage = `<i class="bi bi-search me-2"></i><span>Searching for: <strong>${query}</strong></span>`;
            searchProgressPercent = 30;
            searchProgressMessage = `Searching for: <strong>${query}</strong>`;
        } else if (totalResults === 0) {
            // No results found
            resultMessage = `<i class="bi bi-exclamation-triangle me-2"></i><span>No results found for: <strong>${query}</strong></span>`;
            alertClass = 'alert-warning';
            searchProgressPercent = 100;
            searchProgressMessage = `No results found for: <strong>${query}</strong>`;
            isSearchComplete = true;
        } else if (totalResults === 1) {
            // One result found
            resultMessage = `<i class="bi bi-check-circle me-2"></i><span>1 result found for: <strong>${query}</strong></span>`;
            alertClass = 'alert-success';
            searchProgressPercent = 100;
            searchProgressMessage = `1 result found for: <strong>${query}</strong>`;
            isSearchComplete = true;
        } else {
            // Multiple results found
            resultMessage = `<i class="bi bi-check-circle me-2"></i><span>${totalResults.toLocaleString()} results found for: <strong>${query}</strong></span>`;
            alertClass = 'alert-success';
            searchProgressPercent = 100;
            searchProgressMessage = `${totalResults.toLocaleString()} results found for: <strong>${query}</strong>`;
            isSearchComplete = true;
        }

        // Update the search progress bar
        if (searchProgressContainer) {
            updateSearchProgress(searchProgressPercent, searchProgressMessage, isSearchComplete);
        }

        // Update the alert class
        searchFeedback.className = `alert ${alertClass} d-flex align-items-center mt-2`;

        // Update content
        searchFeedback.innerHTML = `
            <div class="d-flex w-100 justify-content-between align-items-center">
                <div>
                    <div>
                        ${resultMessage}
                    </div>
                    ${optionsDisplay}
                </div>
                <button type="button" class="btn btn-sm btn-outline-secondary" id="clear-search-btn">
                    <i class="bi bi-x-circle"></i> Clear
                </button>
            </div>
        `;

        // Add event listener to clear button
        document.getElementById('clear-search-btn').addEventListener('click', function() {
            searchInput.value = '';
            clearSearchFeedback();
            resetInfiniteScroll();

            // Reset search options
            document.getElementById('regex-search-checkbox').checked = false;
            document.getElementById('case-sensitive-checkbox').checked = false;
            document.getElementById('search-field').value = 'all';
            searchOptions.regex = false;
            searchOptions.caseSensitive = false;
            searchOptions.field = 'all';

            // Hide the search progress bar
            if (searchProgressContainer) {
                toggleGenericProgressBar({ container: searchProgressContainer }, false);
            }

            // Reset to first page when clearing search
            paginationPageNum = 1;

            if (usePagination) {
                loadPaginatedEvents();
            } else {
                loadEvents();
            }
        });

        // Make sure the search button is still visible
        const searchButton = document.querySelector('#search-form button[type="submit"]');
        if (searchButton) {
            searchButton.style.display = 'block';
        }
    }

    // Function to clear search feedback
    function clearSearchFeedback() {
        const searchFeedback = document.getElementById('search-feedback');
        if (searchFeedback) {
            searchFeedback.remove();
        }
    }

    function uploadFile() {
        console.log("DIRECT uploadFile function called");

        // Get the file input element
        const fileInput = document.getElementById('file');

        // Check if a file was selected
        if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
            alert('Please select a file to upload');
            return;
        }

        const file = fileInput.files[0];
        console.log(`Selected file: ${file.name}, type: ${file.type}, size: ${file.size} bytes`);

        // Validate file type
        const isJson = file.type === 'application/json' || file.name.endsWith('.json');
        const isZip = file.type === 'application/zip' || file.name.endsWith('.zip');

        if (!isJson && !isZip) {
            alert('Please select a JSON or ZIP file');
            return;
        }

        // Reset upload start time
        window.uploadStartTime = null;

        // Check if this file already exists in the database
        checkFileStatus(file.name).then(fileStatus => {
            if (fileStatus && fileStatus.exists) {
                const confirmReupload = confirm(
                    `File "${file.name}" already has ${fileStatus.eventCount} events stored. ` +
                    `Do you want to re-upload and replace the existing data?`
                );

                if (!confirmReupload) {
                    console.log('Upload cancelled by user - file already exists');
                    return;
                }
            }

            // For very large files (over 500MB), use chunked upload
            if (file.size > 500 * 1024 * 1024) {
                const confirmChunked = confirm(
                    `The file you selected is very large (${formatFileSize(file.size)}). ` +
                    `Would you like to use chunked upload for better reliability?`
                );

                if (confirmChunked) {
                    uploadLargeFile(file);
                    return;
                }
            }

            // Continue with regular upload
            performFileUpload(file);
        }).catch(error => {
            console.error('Error checking file status:', error);
            // Continue with upload even if status check fails
            performFileUpload(file);
        });
    }

    // Function to check if a file already exists
    async function checkFileStatus(filename) {
        try {
            const response = await fetch(`/api/file-status/${encodeURIComponent(filename)}`);
            const data = await response.json();
            return data.status ? data : null;
        } catch (error) {
            console.error('Error checking file status:', error);
            return null;
        }
    }

    // Function to perform the actual file upload
    function performFileUpload(file) {
        // Create form data
        const formData = new FormData();
        formData.append('file', file);

        // Show loading indicator
        showLoading(true);

        // Create a simple progress indicator directly in the UI
        const progressDiv = document.createElement('div');
        progressDiv.className = 'alert alert-info mt-3';
        progressDiv.innerHTML = `
            <strong>Uploading ${file.name}...</strong>
            <div class="progress mt-2">
                <div class="progress-bar progress-bar-striped progress-bar-animated"
                     role="progressbar" style="width: 0%"
                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>
        `;

        // Find a place to show the progress
        const cardBody = document.querySelector('.card-body');
        if (cardBody) {
            cardBody.appendChild(progressDiv);
        } else {
            // Fallback to body if card-body not found
            document.body.appendChild(progressDiv);
        }

        const progressBar = progressDiv.querySelector('.progress-bar');

        // Use XMLHttpRequest for upload with progress
        const xhr = new XMLHttpRequest();

        // Set longer timeout for large files
        xhr.timeout = 3600000; // 1 hour timeout

        // Handle timeout
        xhr.ontimeout = function() {
            console.error('Upload request timed out');
            progressDiv.className = 'alert alert-danger mt-3';
            progressDiv.innerHTML = `
                <strong>Upload Timeout!</strong> The server took too long to respond.<br>
                <small>Try uploading a smaller file or check your connection.</small>
            `;

            setTimeout(function() {
                progressDiv.remove();
            }, 10000);

            // Hide loading indicator
            showLoading(false);
        };

        // Set up progress tracking with better feedback for large files
        xhr.upload.onprogress = function(e) {
            if (e.lengthComputable) {
                const percent = Math.round((e.loaded / e.total) * 100);
                progressBar.style.width = percent + '%';
                progressBar.setAttribute('aria-valuenow', percent);

                // Show uploaded size for better feedback
                const uploadedSize = formatFileSize(e.loaded);
                const totalSize = formatFileSize(e.total);
                progressBar.textContent = `${percent}% (${uploadedSize} of ${totalSize})`;

                // For large files, add an estimated time remaining
                if (e.total > 50 * 1024 * 1024) { // For files larger than 50MB
                    if (!window.uploadStartTime) {
                        window.uploadStartTime = Date.now();
                    }

                    const elapsedTime = (Date.now() - window.uploadStartTime) / 1000; // in seconds
                    if (percent > 0) {
                        const estimatedTotalTime = (elapsedTime / percent) * 100;
                        const remainingTime = estimatedTotalTime - elapsedTime;

                        // Add time estimate to the progress message
                        const timeMessage = remainingTime > 60
                            ? `Approx. ${Math.round(remainingTime / 60)} min remaining`
                            : `Approx. ${Math.round(remainingTime)} sec remaining`;

                        progressDiv.querySelector('strong').textContent =
                            `Uploading ${file.name}... ${timeMessage}`;
                    }
                }
            }
        };

        // Handle completion
        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    console.log('Upload response:', response);

                    if (response.status) {
                        // Success
                        progressDiv.className = 'alert alert-success mt-3';
                        progressDiv.innerHTML = `
                            <strong>Success!</strong> ${response.message}<br>
                            <small>Refreshing data in 3 seconds...</small>
                        `;

                        // Reset the form
                        const uploadForm = document.getElementById('upload-form');
                        if (uploadForm) {
                            uploadForm.reset();
                        }

                        // Immediately switch to table view
                        showView('table');

                        // Reset state variables immediately
                        currentPage = 0;
                        loadedPages.clear();
                        loadedEvents = {};
                        visibleEvents = [];
                        currentEvents = [];
                        allEventsLoaded = false;

                        // Force a complete reload right away
                        console.log("Forcing complete reload after file upload");
                        loadEvents();

                        // Remove the progress div after a short delay
                        setTimeout(function() {
                            progressDiv.remove();

                            // Make sure we're still showing the table view
                            showView('table');
                        }, 1000);
                    } else {
                        // Error from server
                        progressDiv.className = 'alert alert-danger mt-3';
                        progressDiv.innerHTML = `
                            <strong>Error!</strong> ${response.message}<br>
                            <small>Please try again.</small>
                        `;

                        setTimeout(function() {
                            progressDiv.remove();
                        }, 5000);
                    }
                } catch (e) {
                    // JSON parse error
                    console.error('Error parsing response:', e);
                    progressDiv.className = 'alert alert-danger mt-3';
                    progressDiv.innerHTML = `
                        <strong>Error!</strong> Could not parse server response.<br>
                        <small>Please check the console for details.</small>
                    `;

                    setTimeout(function() {
                        progressDiv.remove();
                    }, 5000);
                }
            } else {
                // HTTP error
                console.error('HTTP error:', xhr.status);

                let errorMessage = 'Server error occurred.';

                try {
                    // Try to parse the error response
                    if (xhr.responseText) {
                        const errorResponse = JSON.parse(xhr.responseText);
                        if (errorResponse.message) {
                            errorMessage = errorResponse.message;
                        }
                    }
                } catch (e) {
                    // If we can't parse the response, use status code specific messages
                    if (xhr.status === 413) {
                        errorMessage = 'File is too large. Maximum file size is 2GB.';
                    } else if (xhr.status === 400) {
                        errorMessage = 'Bad request. The file may be corrupted or invalid.';
                    } else if (xhr.status === 500) {
                        errorMessage = 'Server error. Please try again later.';
                    }
                }

                progressDiv.className = 'alert alert-danger mt-3';
                progressDiv.innerHTML = `
                    <strong>Error (${xhr.status})!</strong> ${errorMessage}<br>
                    <small>Please check the file and try again.</small>
                `;

                setTimeout(function() {
                    progressDiv.remove();
                }, 10000); // Show error for longer (10 seconds)
            }

            // Hide loading indicator
            showLoading(false);
        };

        // Handle network errors
        xhr.onerror = function() {
            console.error('Network error during upload');
            progressDiv.className = 'alert alert-danger mt-3';
            progressDiv.innerHTML = `
                <strong>Network Error!</strong> Could not connect to the server.<br>
                <small>Please check your connection and try again.</small>
            `;

            setTimeout(function() {
                progressDiv.remove();
            }, 5000);

            // Hide loading indicator
            showLoading(false);
        };

        // Open and send the request
        xhr.open('POST', '/api/upload', true);
        xhr.send(formData);

        console.log('Upload request sent');
    }

    /**
     * Creates the upload progress bar
     */
    function createUploadProgressBar() {
        console.log("Creating upload progress bar");

        // Find a suitable container - try multiple options
        let container = document.querySelector('.card-body');
        if (!container) {
            console.log("Could not find .card-body, trying alternative containers");
            container = document.querySelector('.container-fluid') || document.body;
        }

        if (!uploadProgressContainer) {
            console.log("Initializing new upload progress bar");
            const elements = createGenericProgressBar(
                'upload-progress-container',
                'File Upload',
                'Preparing to upload...',
                'upload-progress',
                'primary',
                container
            );

            uploadProgressContainer = elements.container;
            uploadProgressBar = elements.bar;
            uploadProgressText = elements.text;

            // Make sure the progress bar is visible
            if (uploadProgressContainer) {
                uploadProgressContainer.style.zIndex = "1000";
                uploadProgressContainer.style.position = "relative";
                uploadProgressContainer.style.marginTop = "15px";
                uploadProgressContainer.style.marginBottom = "15px";
            }
        } else {
            console.log("Reusing existing upload progress bar");
        }

        // Reset and show the progress bar
        if (uploadProgressBar && uploadProgressText) {
            updateGenericProgressBar({ bar: uploadProgressBar, text: uploadProgressText }, 0, 'Preparing to upload...');
            toggleGenericProgressBar({ container: uploadProgressContainer }, true);
        } else {
            console.error("Upload progress bar elements not properly initialized");
        }
    }

    /**
     * Function to handle chunked upload of large files
     * @param {File} file - The file to upload
     */
    function uploadLargeFile(file) {
        // Show loading indicator
        showLoading(true);

        // Create a progress indicator
        const progressDiv = document.createElement('div');
        progressDiv.className = 'alert alert-info mt-3';
        progressDiv.innerHTML = `
            <strong>Preparing to upload ${file.name} in chunks...</strong>
            <div class="progress mt-2">
                <div class="progress-bar progress-bar-striped progress-bar-animated"
                     role="progressbar" style="width: 0%"
                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>
            <div class="small text-muted mt-1">
                This large file will be uploaded in smaller chunks for better reliability.
            </div>
        `;

        // Find a place to show the progress
        const cardBody = document.querySelector('.card-body');
        if (cardBody) {
            cardBody.appendChild(progressDiv);
        } else {
            document.body.appendChild(progressDiv);
        }

        const progressBar = progressDiv.querySelector('.progress-bar');

        // Set chunk size to 50MB
        const CHUNK_SIZE = 50 * 1024 * 1024;
        const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
        let currentChunk = 0;
        let uploadedSize = 0;

        // Start time for estimating remaining time
        window.uploadStartTime = Date.now();

        // Function to upload a chunk
        function uploadChunk() {
            const start = currentChunk * CHUNK_SIZE;
            const end = Math.min(file.size, start + CHUNK_SIZE);
            const chunk = file.slice(start, end);

            // Create form data for this chunk
            const formData = new FormData();
            formData.append('file', chunk, file.name);
            formData.append('chunk', currentChunk.toString());
            formData.append('totalChunks', totalChunks.toString());

            // Add metadata for the server to recognize this as a chunked upload
            formData.append('chunked', 'true');
            formData.append('originalFileName', file.name);
            formData.append('originalFileSize', file.size.toString());

            // Use XMLHttpRequest for upload with progress
            const xhr = new XMLHttpRequest();
            xhr.timeout = 600000; // 10 minutes timeout per chunk

            // Handle timeout
            xhr.ontimeout = function() {
                console.error(`Chunk ${currentChunk + 1}/${totalChunks} upload timed out`);

                // Ask user if they want to retry this chunk
                if (confirm(`Chunk ${currentChunk + 1} of ${totalChunks} timed out. Retry this chunk?`)) {
                    uploadChunk(); // Retry the same chunk
                } else {
                    progressDiv.className = 'alert alert-danger mt-3';
                    progressDiv.innerHTML = `
                        <strong>Upload Failed!</strong> Chunk ${currentChunk + 1} of ${totalChunks} timed out.<br>
                        <small>The upload was canceled.</small>
                    `;

                    setTimeout(function() {
                        progressDiv.remove();
                    }, 10000);

                    showLoading(false);
                }
            };

            // Track progress for this chunk
            xhr.upload.onprogress = function(e) {
                if (e.lengthComputable) {
                    // Calculate overall progress
                    const chunkProgress = e.loaded / e.total;
                    const overallProgress = (currentChunk + chunkProgress) / totalChunks;
                    const percent = Math.round(overallProgress * 100);

                    // Update progress bar
                    progressBar.style.width = percent + '%';
                    progressBar.setAttribute('aria-valuenow', percent);

                    // Calculate uploaded size
                    const currentUploaded = uploadedSize + e.loaded;
                    progressBar.textContent = `${percent}% (${formatFileSize(currentUploaded)} of ${formatFileSize(file.size)})`;

                    // Estimate time remaining
                    const elapsedTime = (Date.now() - window.uploadStartTime) / 1000; // in seconds
                    if (percent > 0) {
                        const estimatedTotalTime = (elapsedTime / percent) * 100;
                        const remainingTime = estimatedTotalTime - elapsedTime;

                        // Add time estimate to the progress message
                        const timeMessage = remainingTime > 60
                            ? `Approx. ${Math.round(remainingTime / 60)} min remaining`
                            : `Approx. ${Math.round(remainingTime)} sec remaining`;

                        progressDiv.querySelector('strong').textContent =
                            `Uploading chunk ${currentChunk + 1} of ${totalChunks}... ${timeMessage}`;
                    }
                }
            };

            // Handle completion of this chunk
            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);

                        if (response.status) {
                            // Update uploaded size
                            uploadedSize += chunk.size;

                            // Move to next chunk
                            currentChunk++;

                            if (currentChunk < totalChunks) {
                                // Upload next chunk
                                uploadChunk();
                            } else {
                                // All chunks uploaded successfully
                                progressDiv.className = 'alert alert-success mt-3';
                                progressDiv.innerHTML = `
                                    <strong>Success!</strong> ${response.message}<br>
                                    <small>Refreshing data in 3 seconds...</small>
                                `;

                                // Reset the form
                                const uploadForm = document.getElementById('upload-form');
                                if (uploadForm) {
                                    uploadForm.reset();
                                }

                                // Switch to table view and reload events
                                showView('table');
                                resetInfiniteScroll();
                                loadEvents();

                                // Remove the progress div after a delay
                                setTimeout(function() {
                                    progressDiv.remove();
                                }, 3000);
                            }
                        } else {
                            // Error from server
                            progressDiv.className = 'alert alert-danger mt-3';
                            progressDiv.innerHTML = `
                                <strong>Error!</strong> ${response.message}<br>
                                <small>Chunk ${currentChunk + 1} of ${totalChunks} failed. Try again?</small>
                                <button class="btn btn-sm btn-primary mt-2" id="retry-chunk">Retry Chunk</button>
                                <button class="btn btn-sm btn-secondary mt-2 ms-2" id="cancel-upload">Cancel Upload</button>
                            `;

                            // Add event listeners for retry and cancel buttons
                            document.getElementById('retry-chunk').addEventListener('click', function() {
                                uploadChunk(); // Retry the same chunk
                                progressDiv.className = 'alert alert-info mt-3';
                                progressDiv.innerHTML = `
                                    <strong>Retrying chunk ${currentChunk + 1} of ${totalChunks}...</strong>
                                    <div class="progress mt-2">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                             role="progressbar" style="width: 0%"
                                             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                    </div>
                                `;
                            });

                            document.getElementById('cancel-upload').addEventListener('click', function() {
                                progressDiv.remove();
                                showLoading(false);
                            });
                        }
                    } catch (error) {
                        console.error('Error parsing response:', error);
                        progressDiv.className = 'alert alert-danger mt-3';
                        progressDiv.innerHTML = `
                            <strong>Error!</strong> Could not parse server response.<br>
                            <small>Chunk ${currentChunk + 1} of ${totalChunks} failed. Try again?</small>
                            <button class="btn btn-sm btn-primary mt-2" id="retry-chunk">Retry Chunk</button>
                            <button class="btn btn-sm btn-secondary mt-2 ms-2" id="cancel-upload">Cancel Upload</button>
                        `;

                        // Add event listeners for retry and cancel buttons
                        document.getElementById('retry-chunk').addEventListener('click', function() {
                            uploadChunk(); // Retry the same chunk
                            progressDiv.className = 'alert alert-info mt-3';
                            progressDiv.innerHTML = `
                                <strong>Retrying chunk ${currentChunk + 1} of ${totalChunks}...</strong>
                                <div class="progress mt-2">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                         role="progressbar" style="width: 0%"
                                         aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                </div>
                            `;
                        });

                        document.getElementById('cancel-upload').addEventListener('click', function() {
                            progressDiv.remove();
                            showLoading(false);
                        });
                    }
                } else {
                    // HTTP error
                    console.error('HTTP error:', xhr.status);
                    progressDiv.className = 'alert alert-danger mt-3';
                    progressDiv.innerHTML = `
                        <strong>Error (${xhr.status})!</strong> Server error during upload.<br>
                        <small>Chunk ${currentChunk + 1} of ${totalChunks} failed. Try again?</small>
                        <button class="btn btn-sm btn-primary mt-2" id="retry-chunk">Retry Chunk</button>
                        <button class="btn btn-sm btn-secondary mt-2 ms-2" id="cancel-upload">Cancel Upload</button>
                    `;

                    // Add event listeners for retry and cancel buttons
                    document.getElementById('retry-chunk').addEventListener('click', function() {
                        uploadChunk(); // Retry the same chunk
                        progressDiv.className = 'alert alert-info mt-3';
                        progressDiv.innerHTML = `
                            <strong>Retrying chunk ${currentChunk + 1} of ${totalChunks}...</strong>
                            <div class="progress mt-2">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" style="width: 0%"
                                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                            </div>
                        `;
                    });

                    document.getElementById('cancel-upload').addEventListener('click', function() {
                        progressDiv.remove();
                        showLoading(false);
                    });
                }
            };

            // Handle network errors
            xhr.onerror = function() {
                console.error('Network error during chunk upload');
                progressDiv.className = 'alert alert-danger mt-3';
                progressDiv.innerHTML = `
                    <strong>Network Error!</strong> Could not connect to the server.<br>
                    <small>Chunk ${currentChunk + 1} of ${totalChunks} failed. Try again?</small>
                    <button class="btn btn-sm btn-primary mt-2" id="retry-chunk">Retry Chunk</button>
                    <button class="btn btn-sm btn-secondary mt-2 ms-2" id="cancel-upload">Cancel Upload</button>
                `;

                // Add event listeners for retry and cancel buttons
                document.getElementById('retry-chunk').addEventListener('click', function() {
                    uploadChunk(); // Retry the same chunk
                    progressDiv.className = 'alert alert-info mt-3';
                    progressDiv.innerHTML = `
                        <strong>Retrying chunk ${currentChunk + 1} of ${totalChunks}...</strong>
                        <div class="progress mt-2">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar" style="width: 0%"
                                 aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                    `;
                });

                document.getElementById('cancel-upload').addEventListener('click', function() {
                    progressDiv.remove();
                    showLoading(false);
                });
            };

            // Open and send the request
            xhr.open('POST', '/api/upload-chunk', true);
            xhr.send(formData);
        }

        // Start uploading the first chunk
        uploadChunk();
    }

    /**
     * Updates the upload progress bar
     * @param {number} percent - Percentage of completion (0-100)
     * @param {string} message - Message to display
     * @param {boolean} isComplete - Whether the upload is complete
     * @param {boolean} isError - Whether there was an error
     */
    function updateUploadProgress(percent, message, isComplete = false, isError = false) {
        updateGenericProgressBar(
            { bar: uploadProgressBar, text: uploadProgressText },
            percent,
            message,
            isComplete,
            isError
        );

        // Hide the progress bar after completion with a delay
        if (isComplete) {
            setTimeout(() => {
                toggleGenericProgressBar({ container: uploadProgressContainer }, false);
            }, 3000);
        }
    }

    function resetInfiniteScroll() {
        // Clear any continuous loading interval
        if (window.continuousLoadingInterval) {
            clearInterval(window.continuousLoadingInterval);
            window.continuousLoadingInterval = null;
        }

        currentSearchParams.from = 0;
        loadedEvents = {};
        loadedPages.clear();
        currentPage = 0;
        visibleEvents = [];
        hasMoreEvents = true;
        eventsTableBody.innerHTML = '';

        // Reset progress indicators with null checks
        const showingText = document.querySelector('#showing-info .d-flex .text-white');
        if (showingText) {
            showingText.textContent = 'Showing 0 of 0 events';
        }

        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', 0);
        }

        const loadedPercentage = document.getElementById('loaded-percentage');
        if (loadedPercentage) {
            loadedPercentage.textContent = '0% loaded';
        }

        const loadedPagesText = document.getElementById('loaded-pages');
        if (loadedPagesText) {
            loadedPagesText.textContent = '0 pages loaded';
        }

        // Start continuous loading again after a short delay
        setTimeout(() => {
            if (hasMoreEvents && !window.continuousLoadingInterval) {
                window.continuousLoadingInterval = setInterval(() => {
                    if (!isLoading && hasMoreEvents) {
                        const totalLoaded = Array.from(loadedPages).reduce((total, p) => total + (loadedEvents[p]?.length || 0), 0);
                        const percentLoaded = Math.round((totalLoaded / totalEvents) * 100);

                        // If we've loaded less than 50% of the data, keep loading more
                        if (percentLoaded < 50) {
                            console.log(`Continuous loading: ${percentLoaded}% loaded so far`);
                            loadMoreEvents();
                        } else {
                            // Once we've loaded enough, clear the interval
                            clearInterval(window.continuousLoadingInterval);
                            window.continuousLoadingInterval = null;
                        }
                    }
                }, 3000); // Check every 3 seconds
            }
        }, 5000);
    }

    function resetAndRetry() {
        // Clear any error messages
        loadingMore.style.display = 'none';
        loadingMore.innerHTML = '';

        // Reset the current retries
        currentRetries = 0;

        // Reduce the page size for better performance only if it's extremely large
        if (pageSize > 5000) {
            console.log(`Reducing page size from ${pageSize} to 5000 for better performance`);
            pageSize = 5000;
        }

        // Reset the loading state
        isLoading = false;

        // Try loading the first page again
        loadPage(0, true);
    }

    function checkVisibleRows() {
        // Get all visible rows
        const rows = eventsTableBody.querySelectorAll('tr.event-row');
        if (rows.length === 0) return;

        // Get the last visible row
        const lastRow = rows[rows.length - 1];
        const lastRowPage = parseInt(lastRow.dataset.page || '0');

        // If we're close to the last loaded page, load the next page
        const maxLoadedPage = loadedPages.size > 0 ? Math.max(...Array.from(loadedPages)) : 0;
        const maxPage = Math.ceil(totalEvents / pageSize) - 1;

        // More aggressive loading - load next page when we're within 3 pages of the last loaded page
        if (lastRowPage >= maxLoadedPage - 3 && maxLoadedPage < maxPage && !isLoading) {
            console.log(`Loading next page ${maxLoadedPage + 1} because we're close to the end`);
            loadMoreEvents();
        }

        // If we've loaded less than 10% of the total pages, load more aggressively
        const loadedPagesCount = loadedPages.size;
        const totalPages = Math.ceil(totalEvents / pageSize);

        if (loadedPagesCount < totalPages * 0.1 && !isLoading && hasMoreEvents) {
            console.log(`Loading more pages aggressively (${loadedPagesCount} of ${totalPages} loaded)`);
            setTimeout(() => loadMoreEvents(), 500);
        }
    }

    async function toggleFilterDropdown(columnId, iconElement) {
        try {
            // Close any open filter dropdown
            if (activeFilterDropdown) {
                closeFilterDropdown();
            }

        // Create a new filter dropdown (async)
        const dropdown = await createFilterDropdown(columnId);

        // Position the dropdown below the filter icon
        const rect = iconElement.getBoundingClientRect();
        const viewportWidth = window.innerWidth;

        // Set top position
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;

        // Calculate left position to ensure dropdown is visible
        // For timestamp and left columns, position to the right of the icon
        // For right columns, position to the left of the icon
        const dropdownWidth = 250; // Width of the dropdown

        // Check if positioning to the right would go off-screen
        if (rect.left + dropdownWidth > viewportWidth) {
            // Position to the left of the icon
            dropdown.style.left = `${rect.right - dropdownWidth + window.scrollX}px`;
        } else {
            // Position to the right of the icon
            dropdown.style.left = `${rect.left + window.scrollX}px`;
        }

        // Show the dropdown
        document.body.appendChild(dropdown);
        dropdown.classList.add('show');

        // Set as active dropdown
        activeFilterDropdown = dropdown;
        } catch (error) {
            console.error('Error in toggleFilterDropdown:', error);
            // Close any open dropdown on error
            if (activeFilterDropdown) {
                closeFilterDropdown();
            }
        }
    }

    function closeFilterDropdown() {
        if (activeFilterDropdown) {
            activeFilterDropdown.remove();
            activeFilterDropdown = null;
        }
    }

    async function createFilterDropdown(columnId) {
        // Clone the template
        const dropdown = document.getElementById('filter-dropdown-template').cloneNode(true);
        dropdown.id = `filter-dropdown-${columnId}`;
        dropdown.style.display = 'block';

        // Set the column title
        const column = availableColumns.find(col => col.id === columnId);
        dropdown.querySelector('.filter-title').textContent = `Filter: ${column.label}`;

        // Store the column ID
        dropdown.dataset.columnId = columnId;

        // Show loading indicator in the filter list
        const filterList = dropdown.querySelector('.filter-list');
        filterList.innerHTML = `
            <li class="filter-item text-center">
                <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Loading values...</span>
            </li>
        `;

        // Get unique values for this column (async)
        try {
            const uniqueValues = await getUniqueColumnValues(columnId);

            // Clear loading indicator and populate the filter list
            filterList.innerHTML = '';

            if (uniqueValues.length === 0) {
                filterList.innerHTML = `
                    <li class="filter-item text-center text-muted">
                        No values found
                    </li>
                `;
            } else {
                uniqueValues.forEach(value => {
                    const li = document.createElement('li');
                    li.className = 'filter-item';

                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.className = 'form-check-input me-2';
                    checkbox.value = value;
                    checkbox.checked = !columnFilters[columnId] || columnFilters[columnId].includes(value);

                    const label = document.createElement('label');
                    label.className = 'form-check-label';
                    label.textContent = value || '(Empty)';

                    li.appendChild(checkbox);
                    li.appendChild(label);
                    filterList.appendChild(li);
                });
            }
        } catch (error) {
            console.error('Error loading filter values:', error);
            filterList.innerHTML = `
                <li class="filter-item text-center text-danger">
                    Error loading values
                </li>
            `;
        }

        // Add event listeners
        const selectAllCheckbox = dropdown.querySelector('.filter-select-all');
        const filterSearch = dropdown.querySelector('.filter-search');
        const applyButton = dropdown.querySelector('.filter-apply');
        const cancelButton = dropdown.querySelector('.filter-cancel');
        const closeButton = dropdown.querySelector('.filter-close');

        // Select all checkbox
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = filterList.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        });

        // Filter search
        filterSearch.addEventListener('input', function() {
            const searchText = filterSearch.value.toLowerCase();
            const items = filterList.querySelectorAll('.filter-item');

            items.forEach(item => {
                const label = item.querySelector('label').textContent.toLowerCase();
                if (label.includes(searchText)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // Apply button
        applyButton.addEventListener('click', function() {
            applyFilter(columnId, dropdown);
        });

        // Cancel button
        cancelButton.addEventListener('click', function() {
            closeFilterDropdown();
        });

        // Close button
        closeButton.addEventListener('click', function() {
            closeFilterDropdown();
        });

        return dropdown;
    }

    async function getUniqueColumnValues(columnId) {
        try {
            // Fetch unique values from the server for the specified column
            const response = await fetch(`/api/unique-values/${columnId}`);
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const data = await response.json();
            if (data.status && data.values) {
                return data.values;
            } else {
                console.error('Error fetching unique values:', data.message);
                return [];
            }
        } catch (error) {
            console.error('Error fetching unique values:', error);

            // Fallback to current events if server request fails
            const values = new Set();
            const timestampMap = new Map(); // For storing timestamp values with their original dates

            currentEvents.forEach(event => {
                let value;

                if (columnId === 'timestamp') {
                    // Format timestamp directly with UTC+3 timezone
                    const date = new Date(event.timestamp);
                    if (!isNaN(date.getTime())) {
                        try {
                            // Format the date in UTC+3 timezone
                            const options = {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit',
                                hour12: false,
                                timeZone: 'Europe/Moscow' // UTC+3 timezone
                            };

                            // Format the date with the specified options
                            const formattedDate = date.toLocaleString('en-US', options);

                            // Add milliseconds
                            value = formattedDate + '.' + String(date.getMilliseconds()).padStart(3, '0') + ' (UTC+3)';
                        } catch (error) {
                            // Fallback to standard formatting if timezone formatting fails
                            value = date.toLocaleString() + '.' + String(date.getMilliseconds()).padStart(3, '0');
                        }
                    } else {
                        value = event.timestamp || 'N/A';
                    }
                    // Store the original date with the formatted string for proper sorting
                    timestampMap.set(value, new Date(event.timestamp));
                } else if (columnId.startsWith('details.')) {
                    const fieldPath = columnId.substring(8);
                    const fieldValue = getNestedValue(event.details, fieldPath);

                    if (fieldValue === undefined) {
                        value = '';
                    } else if (typeof fieldValue === 'object') {
                        value = JSON.stringify(fieldValue);
                    } else {
                        value = String(fieldValue);
                    }
                } else {
                    value = String(event[columnId] || '');
                }

                values.add(value);
            });

            // Convert to array for sorting
            const valuesArray = Array.from(values);

            // Sort based on column type
            if (columnId === 'timestamp') {
                // Sort timestamps chronologically
                return valuesArray.sort((a, b) => {
                    const dateA = timestampMap.get(a);
                    const dateB = timestampMap.get(b);
                    return dateA - dateB;
                });
            } else {
                // Default string sorting for other columns
                return valuesArray.sort((a, b) => {
                    // Try to sort numerically if both values are numbers
                    const numA = parseFloat(a);
                    const numB = parseFloat(b);

                    if (!isNaN(numA) && !isNaN(numB)) {
                        return numA - numB;
                    }

                    // Otherwise sort alphabetically
                    return a.localeCompare(b);
                });
            }
        }
    }

    function applyFilter(columnId, dropdown) {
        // Get selected values
        const checkboxes = dropdown.querySelectorAll('.filter-list input[type="checkbox"]:checked');
        const selectedValues = Array.from(checkboxes).map(checkbox => checkbox.value);

        // Update column filters
        if (selectedValues.length === 0) {
            // If no values are selected, remove the filter
            delete columnFilters[columnId];
        } else {
            // Otherwise, set the filter
            columnFilters[columnId] = selectedValues;
        }

        // Close the dropdown
        closeFilterDropdown();

        // Create a filter tag to show the active filter
        const filterTag = document.createElement('span');
        filterTag.className = 'badge bg-info me-2 mb-2';
        filterTag.innerHTML = `
            ${getColumnLabel(columnId)}: ${selectedValues.length} selected
            <button type="button" class="btn-close btn-close-white ms-2" aria-label="Remove"
                    onclick="removeFilter('${columnId}')"></button>
        `;

        // Add the filter tag to the search tags container
        const searchTags = document.getElementById('search-tags');
        if (searchTags) {
            // Remove any existing tag for this column
            const existingTag = searchTags.querySelector(`[data-column-id="${columnId}"]`);
            if (existingTag) {
                existingTag.remove();
            }

            // Add the new tag
            filterTag.dataset.columnId = columnId;
            searchTags.appendChild(filterTag);
        }

        // Update filter icon
        const filterIcon = document.querySelector(`.filter-icon[data-column-id="${columnId}"]`);
        if (filterIcon) {
            if (columnFilters[columnId]) {
                filterIcon.classList.add('filter-active');
            } else {
                filterIcon.classList.remove('filter-active');
            }
        }

        // Apply the filters to all events by reloading
        isSearchMode = Object.keys(columnFilters).length > 0;

        console.log(`🔍 Filter applied for ${columnId}. Active filters:`, Object.keys(columnFilters));
        console.log('Search mode:', isSearchMode);

        // Reset to first page when filtering
        paginationPageNum = 1;

        // Reload events with the new filter
        if (usePagination) {
            console.log('Loading paginated events with filters...');
            loadPaginatedEvents();
        } else {
            console.log('Loading infinite scroll events with filters...');
            resetInfiniteScroll();
            loadEvents();
        }

        // Update showing info
        updateShowingInfo();
    }

    // Helper function to get a column label
    function getColumnLabel(columnId) {
        const column = availableColumns.find(col => col.id === columnId);
        return column ? column.label : columnId;
    }

    // Function to remove a filter
    function removeFilter(columnId) {
        // Remove the filter
        delete columnFilters[columnId];

        // Remove the filter tag
        const searchTags = document.getElementById('search-tags');
        if (searchTags) {
            const existingTag = searchTags.querySelector(`[data-column-id="${columnId}"]`);
            if (existingTag) {
                existingTag.remove();
            }
        }

        // Apply the filters to all events by reloading
        isSearchMode = Object.keys(columnFilters).length > 0;

        // Reset to first page when removing filters
        paginationPageNum = 1;

        // Load events with the updated filters
        if (usePagination) {
            loadPaginatedEvents();
        } else {
            resetInfiniteScroll();
            loadEvents();
        }
    }

    // Helper function to build a filter query for any column
    function buildFilterQuery(columnId, selectedValues) {
        // For timestamp column, use the existing function
        if (columnId === 'timestamp') {
            return buildTimestampFilterQuery(selectedValues);
        }

        // For other columns, create a query with OR operators
        // Escape special characters and wrap in quotes
        return selectedValues.map(value => {
            // Handle empty values
            if (!value) {
                return `${columnId}:""`;
            }

            // Escape special characters in the value
            const escapedValue = value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

            // Return the column:value format for exact matching
            return `${columnId}:"${escapedValue}"`;
        }).join(' OR ');
    }

    // Helper function to build a timestamp filter query
    function buildTimestampFilterQuery(selectedTimestamps) {
        // Join the selected timestamps with OR operator
        return selectedTimestamps.map(timestamp => {
            // Extract the date part for more flexible matching
            const date = new Date(timestamp);
            const dateStr = date.toISOString().split('T')[0]; // Get YYYY-MM-DD format
            return dateStr;
        }).join(' OR ');
    }

    function loadEvents() {
        console.log("loadEvents called - starting to load events");
        console.log("- usePagination:", usePagination);
        console.log("- paginationPageNum:", paginationPageNum);
        console.log("- currentSearchParams:", currentSearchParams);

        // Make sure we're showing the table view
        showView('table');

        // Clear any continuous loading interval
        if (window.continuousLoadingInterval) {
            clearInterval(window.continuousLoadingInterval);
            window.continuousLoadingInterval = null;
        }

        // Initialize the load more button if not already done
        if (!loadMoreBtn) {
            loadMoreBtn = document.getElementById('load-more-btn');
        }

        // If using pagination, load the current page
        if (usePagination) {
            console.log("Using pagination mode - calling loadPaginatedEvents()");
            loadPaginatedEvents();
            return;
        }

        // Initialize progress elements
        const progressBar = document.getElementById('progress-bar');
        const loadedPercentage = document.getElementById('loaded-percentage');
        const loadedPagesText = document.getElementById('loaded-pages');

        // Reset progress indicators
        if (progressBar) {
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', 0);
        }

        if (loadedPercentage) {
            loadedPercentage.textContent = '0% loaded';
        }

        if (loadedPagesText) {
            loadedPagesText.textContent = '0 pages loaded';
        }

        try {
            // Initialize the load progress container if needed
            if (!loadProgressContainer) {
                // Find a suitable container for the progress bar
                let targetContainer = document.getElementById('events-table-container') ||
                                     document.querySelector('.container-fluid') ||
                                     document.body;

                const elements = createGenericProgressBar(
                    'load-progress-container',
                    'Loading Events',
                    'Preparing to load events...',
                    'load-progress',
                    'primary',
                    targetContainer
                );

                loadProgressContainer = elements.container;
                loadProgressBar = elements.bar;
                loadProgressText = elements.text;
            }

            // Show loading indicators but don't block the UI
            if (loadProgressBar && loadProgressText) {
                updateGenericProgressBar({ bar: loadProgressBar, text: loadProgressText }, 0, 'Initializing event loading...');
                toggleGenericProgressBar({ container: loadProgressContainer }, true);
            }
        } catch (error) {
            console.error('Error initializing progress bar:', error);
            // Continue without the progress bar
        }

        // Make the app immediately usable
        document.body.classList.remove('loading');

        // Reset state variables
        isLoading = true;
        currentPage = 0;
        loadedPages.clear();
        loadedEvents = {};
        visibleEvents = [];
        currentEvents = []; // Clear current events
        allEventsLoaded = false; // Reset the flag for all events loaded

        // Log the current state for debugging
        console.log("State reset, preparing to load first page of events");

        // Show a loading indicator in the table that doesn't block interaction
        eventsTableBody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">Loading events...</p>
                    <p class="text-muted">The app is usable while events load in the background</p>
                </td>
            </tr>
        `;

        // Make sure we're showing the table view
        showView('table');

        // First, load the initial page to get the total count
        // Use setTimeout to prevent UI blocking
        setTimeout(() => {
            loadPage(0, true, () => {
                // After the initial page is loaded, start loading all pages in the background
                loadAllPagesInBackground();

                // Make sure we're showing the table view after loading the first page
                showView('table');
            });
        }, 100);

        // Make sure loadMoreBtn is initialized
        if (!loadMoreBtn) {
            loadMoreBtn = document.getElementById('load-more-btn');
        }

        // Hide the load more button initially
        if (loadMoreBtn) {
            loadMoreBtn.style.display = 'none';
        }

        // Show pagination controls if in pagination mode
        if (paginationControls) {
            paginationControls.style.display = usePagination ? 'block' : 'none';
        }
    }

    // Function to navigate to a specific page
    function navigateToPage(pageNum) {
        if (pageNum < 1 || pageNum > totalPaginationPages) {
            console.warn(`Invalid page number: ${pageNum}`);
            return;
        }

        console.log(`Navigating to page ${pageNum}`);
        paginationPageNum = pageNum;

        // Update page jump input
        if (pageJumpInput) {
            pageJumpInput.value = pageNum;
        }

        // Load the page
        loadPaginatedEvents();
    }

    // Function to update pagination controls
    function updatePaginationControls() {
        if (!usePagination) return;

        // Update page numbers
        if (currentPageNum) {
            currentPageNum.textContent = paginationPageNum;
        }
        if (totalPagesNum) {
            totalPagesNum.textContent = totalPaginationPages;
        }

        // Update button states
        const isFirstPage = paginationPageNum === 1;
        const isLastPage = paginationPageNum === totalPaginationPages;

        // First and Previous buttons
        if (paginationFirst) {
            paginationFirst.classList.toggle('disabled', isFirstPage);
        }
        if (paginationPrev) {
            paginationPrev.classList.toggle('disabled', isFirstPage);
        }

        // Next and Last buttons
        if (paginationNext) {
            paginationNext.classList.toggle('disabled', isLastPage);
        }
        if (paginationLast) {
            paginationLast.classList.toggle('disabled', isLastPage);
        }

        // Update page jump input max value
        if (pageJumpInput) {
            pageJumpInput.max = totalPaginationPages;
            pageJumpInput.value = paginationPageNum;
        }

        // Show/hide pagination controls
        if (paginationControls) {
            paginationControls.style.display = usePagination ? 'block' : 'none';
        }
    }

    function updatePaginationProgress() {
        // Update progress bar for pagination
        if (usePagination) {
            const progressBar = document.getElementById('progress-bar');
            const loadedPercentage = document.getElementById('loaded-percentage');
            const loadedPages = document.getElementById('loaded-pages');

            if (progressBar) {
                progressBar.style.width = '100%';
                progressBar.setAttribute('aria-valuenow', 100);
            }

            if (loadedPercentage) {
                loadedPercentage.textContent = '100% loaded';
            }

            if (loadedPages) {
                loadedPages.textContent = `Page ${paginationPageNum} of ${totalPaginationPages}`;
            }
        }
    }

    // Function to load all pages in the background
    function loadAllPagesInBackground() {
        console.log("Starting background loading of all pages");

        // Calculate the total number of pages
        const totalPages = Math.ceil(totalEvents / RECORDS_PER_PAGE);
        console.log(`Total pages to load: ${totalPages}`);

        // Create a queue of pages to load
        const pagesToLoad = [];
        for (let i = 0; i < totalPages; i++) {
            if (!loadedPages.has(i)) {
                pagesToLoad.push(i);
            }
        }

        // If all pages are already loaded, we're done
        if (pagesToLoad.length === 0) {
            console.log("All pages already loaded");
            allEventsLoaded = true;
            updateLoadingStatus();
            return;
        }

        console.log(`Pages to load: ${pagesToLoad.length}`);

        // Load pages in batches to avoid overwhelming the server
        const batchSize = 5;
        let currentBatchIndex = 0;

        // Function to load a batch of pages
        function loadBatch() {
            if (currentBatchIndex >= pagesToLoad.length) {
                console.log("All pages loaded successfully");
                allEventsLoaded = true;
                updateLoadingStatus();
                return;
            }

            const batch = pagesToLoad.slice(currentBatchIndex, currentBatchIndex + batchSize);
            currentBatchIndex += batchSize;

            console.log(`Loading batch of ${batch.length} pages (${currentBatchIndex - batch.length + 1}-${currentBatchIndex} of ${pagesToLoad.length})`);

            // Load each page in the batch
            let completedInBatch = 0;
            batch.forEach(page => {
                // Skip if already loaded
                if (loadedPages.has(page)) {
                    completedInBatch++;
                    if (completedInBatch === batch.length) {
                        // Move to the next batch
                        setTimeout(loadBatch, 100);
                    }
                    return;
                }

                // Load the page without rendering (silent load)
                loadPage(page, false, () => {
                    completedInBatch++;
                    updateLoadingStatus();

                    // If all pages in this batch are loaded, move to the next batch
                    if (completedInBatch === batch.length) {
                        // Add a small delay to avoid overwhelming the server
                        setTimeout(loadBatch, 100);
                    }
                }, true); // Silent load
            });
        }

        // Start loading the first batch
        loadBatch();
    }

    // Function to update the loading status
    function updateLoadingStatus() {
        const totalLoaded = Array.from(loadedPages).reduce((total, p) => total + (loadedEvents[p]?.length || 0), 0);
        const percentLoaded = totalEvents > 0 ? Math.min(Math.round((totalLoaded / totalEvents) * 100), 100) : 0;
        const pagesLoaded = Array.from(loadedPages).length;
        const totalPages = Math.ceil(totalEvents / RECORDS_PER_PAGE);
        const isComplete = allEventsLoaded || percentLoaded >= 100;

        // Update the standard progress bar elements
        const progressBar = document.getElementById('progress-bar');
        const loadedPercentage = document.getElementById('loaded-percentage');
        const loadedPagesText = document.getElementById('loaded-pages');

        if (progressBar) {
            progressBar.style.width = `${percentLoaded}%`;
            progressBar.setAttribute('aria-valuenow', percentLoaded);
        }

        if (loadedPercentage) {
            loadedPercentage.textContent = `${percentLoaded}% loaded`;
        }

        if (loadedPagesText) {
            loadedPagesText.textContent = `${pagesLoaded} of ${totalPages} pages loaded`;
        }

        // Update the showing info text
        const showingText = document.querySelector('#showing-info .d-flex .text-white');
        if (showingText) {
            showingText.textContent = `Showing ${totalLoaded.toLocaleString()} of ${totalEvents.toLocaleString()} events`;
        }

        // Update our animated load progress bar
        const loadMessage = `
            <div class="d-flex justify-content-between align-items-center">
                <span>Loaded ${totalLoaded.toLocaleString()} of ${totalEvents.toLocaleString()} events</span>
                <span class="badge bg-info">${percentLoaded}% Complete</span>
            </div>
            <div class="small text-muted mt-1">
                ${pagesLoaded} of ${totalPages} pages loaded
            </div>
        `;

        updateLoadProgress(percentLoaded, loadMessage, isComplete);

        // If all events are loaded, update the UI
        if (isComplete) {
            console.log("All events loaded, updating UI");
            allEventsLoaded = true;

            // If we're in pagination mode, render the current page
            if (usePagination) {
                renderPage(paginationPageNum - 1);
            } else {
                // In non-pagination mode, render all events
                renderAllEvents();
            }

            // Show a completion message
            loadingMore.style.display = 'block';
            loadingMore.innerHTML = `
                <div class="alert alert-success">
                    All ${totalEvents.toLocaleString()} events loaded successfully!
                </div>
            `;

            // Hide the message after 3 seconds
            setTimeout(() => {
                loadingMore.style.display = 'none';
                loadingMore.innerHTML = '';
            }, 3000);

            // Hide loading spinner
            showLoading(false);
        }
    }

    function loadPage(page, isInitialLoad = false, callback = null, silentLoad = false) {
        // Prevent loading if already loading and not initial load
        if (isLoading && !isInitialLoad && !silentLoad) return;

        // If page is already loaded, just render it and call the callback
        if (loadedPages.has(page)) {
            console.log(`Page ${page + 1} already loaded, just rendering it`);
            if (!silentLoad) {
                renderPage(page);
            }
            if (callback) {
                callback();
            }
            return;
        }

        // Set loading state if not silent
        if (!silentLoad) {
            isLoading = true;
        }

        // Show loading indicator if not silent
        if (!isInitialLoad && !silentLoad) {
            loadingMore.style.display = 'block';
            loadingMore.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-2 text-white" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="loading-message">
                        <div class="text-white">Loading page ${page + 1}...</div>
                    </div>
                </div>
            `;
        }

        // Calculate the range of events to load
        let from, effectivePageSize;

        if (usePagination) {
            // In pagination mode, use RECORDS_PER_PAGE (50) and calculate from based on pagination page number
            from = (page * RECORDS_PER_PAGE);
            effectivePageSize = RECORDS_PER_PAGE;
            console.log(`Pagination mode: Loading page ${page + 1} with ${RECORDS_PER_PAGE} records per page`);
        } else {
            // In infinite scroll mode, use the regular pageSize
            from = page * pageSize;

            // For very large datasets, use a smaller page size only if extremely large
            effectivePageSize = pageSize;
            if (totalEvents > 1000000 && pageSize > 5000) {
                console.log(`Extremely large dataset detected (${totalEvents.toLocaleString()} events), reducing page size from ${pageSize} to 5000`);
                effectivePageSize = 5000;
            }
        }

        // Build query parameters
        const queryParams = new URLSearchParams({
            query: currentSearchParams.query || '',
            from: from,
            size: effectivePageSize,
            sort: currentSearchParams.sort || 'timestamp:asc',
            _t: Date.now() // Prevent caching
        });

        // Add search options - check both old and new format
        if (currentSearchParams.searchOptions) {
            if (currentSearchParams.searchOptions.regex) {
                queryParams.append('regex', 'true');
            }

            if (currentSearchParams.searchOptions.caseSensitive) {
                queryParams.append('caseSensitive', 'true');
            }

            if (currentSearchParams.searchOptions.field && currentSearchParams.searchOptions.field !== 'all') {
                queryParams.append('field', currentSearchParams.searchOptions.field);
            }
        } else {
            // New format - direct properties
            if (currentSearchParams.regex) {
                queryParams.append('regex', 'true');
            }

            if (currentSearchParams.caseSensitive) {
                queryParams.append('caseSensitive', 'true');
            }

            if (currentSearchParams.field && currentSearchParams.field !== 'all') {
                queryParams.append('field', currentSearchParams.field);
            }
        }

        console.log('🔍 Final query URL:', `/api/events?${queryParams}`);

        console.log(`Loading page ${page + 1} (events ${from} to ${from + effectivePageSize})`);

        // Use AbortController to allow canceling the request if needed
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
            console.log('Request timeout, aborting...');
            controller.abort();
        }, 60000); // 60 second timeout (increased from 30s)

        // Make the request with error handling
        fetch(`/api/events?${queryParams}`, {
            signal: controller.signal,
            headers: {
                'Cache-Control': 'no-cache'
            }
        })
            .then(response => {
                clearTimeout(timeoutId);
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log(`Received page ${page + 1} with ${data.events ? data.events.length : 0} events`);

                if (!data) {
                    console.error('Invalid data format received:', data);
                    throw new Error('Invalid data format received from server');
                }

                // Handle case where no events are returned
                if (!data.events) {
                    console.warn('No events property in response:', data);
                    data.events = [];
                }

                console.log(`Received data with ${data.events.length} events, total: ${data.total}`);
                totalEvents = data.total || 0;

                // If we have no events but total is greater than 0, something might be wrong
                if (data.events.length === 0 && data.total > 0) {
                    console.warn('Received 0 events but total count is greater than 0, this might indicate a data loading issue');
                }

                // If we have no events at all, show a message in the table
                if (data.events.length === 0 && data.total === 0) {
                    eventsTableBody.innerHTML = `
                        <tr>
                            <td colspan="10" class="text-center py-5">
                                <p>No events found. Please upload a file or parse a folder.</p>
                            </td>
                        </tr>
                    `;
                    showView('table');

                    // Update the showing info
                    const showingText = document.querySelector('#showing-info .d-flex .text-white');
                    if (showingText) {
                        showingText.textContent = 'No events available';
                    }

                    // Update progress indicators
                    const progressBar = document.getElementById('progress-bar');
                    if (progressBar) {
                        progressBar.style.width = '0%';
                        progressBar.setAttribute('aria-valuenow', 0);
                    }

                    const loadedPercentage = document.getElementById('loaded-percentage');
                    if (loadedPercentage) {
                        loadedPercentage.textContent = '0% loaded';
                    }

                    const loadedPagesText = document.getElementById('loaded-pages');
                    if (loadedPagesText) {
                        loadedPagesText.textContent = '0 pages loaded';
                    }
                }

                // Store the loaded page
                loadedEvents[page] = data.events;
                loadedPages.add(page);

                // If no events found and this is the first page, show a message
                if (data.events.length === 0 && isInitialLoad) {
                    eventsTableBody.innerHTML = `
                        <tr>
                            <td colspan="10" class="text-center py-5">
                                <p class="text-muted">No events found${currentSearchParams.query ? ' matching your search criteria' : ''}.</p>
                            </td>
                        </tr>
                    `;
                } else {
                    // Render the page
                    if (isInitialLoad) {
                        // Clear the table for initial load
                        eventsTableBody.innerHTML = '';
                    }

                    renderPage(page);
                }

                // Update showing info with formatted numbers and progress indicators
                const totalLoaded = Array.from(loadedPages).reduce((total, p) => total + (loadedEvents[p]?.length || 0), 0);

                // Update the main text
                const showingText = document.querySelector('#showing-info .d-flex .text-white');
                if (showingText) {
                    showingText.textContent = `Showing ${totalLoaded.toLocaleString()} of ${totalEvents.toLocaleString()} events`;
                }

                // Update search feedback with the total count if this is a search
                if (isInitialLoad && currentSearchParams.query) {
                    // Update search progress to 60% when we get the first page
                    if (searchProgressContainer) {
                        updateSearchProgress(60, `Processing search results for: <strong>${currentSearchParams.query}</strong>`, false);
                    }

                    // Update search feedback with the total count
                    showSearchFeedback(currentSearchParams.query, totalEvents);
                }

                // Calculate and update the percentage - ensure it's accurate for large datasets
                let percentage = 0;
                if (totalEvents > 0) {
                    // Calculate percentage with higher precision for large datasets
                    percentage = Math.min(Math.round((totalLoaded / totalEvents) * 100), 100);

                    // If we've loaded at least one page but percentage is 0, show at least 1%
                    if (percentage === 0 && loadedPages.size > 0) {
                        percentage = 1;
                    }
                }

                // Update the progress bar
                const progressBar = document.getElementById('progress-bar');
                progressBar.style.width = `${percentage}%`;
                progressBar.setAttribute('aria-valuenow', percentage);

                // Update the percentage text
                const loadedPercentage = document.getElementById('loaded-percentage');
                loadedPercentage.textContent = `${percentage}% loaded`;

                // Update the pages loaded text
                const loadedPagesText = document.getElementById('loaded-pages');
                const pagesLoaded = Array.from(loadedPages).length;

                // Calculate total pages differently based on pagination mode
                let totalPages;
                if (usePagination) {
                    totalPages = Math.ceil(totalEvents / RECORDS_PER_PAGE);
                    totalPaginationPages = totalPages; // Update the global variable

                    // Update pagination UI
                    currentPageNum.textContent = paginationPageNum;
                    totalPagesNum.textContent = totalPages;
                    pageJumpInput.max = totalPages;

                    // Update pagination buttons state
                    paginationFirst.classList.toggle('disabled', paginationPageNum <= 1);
                    paginationPrev.classList.toggle('disabled', paginationPageNum <= 1);
                    paginationNext.classList.toggle('disabled', paginationPageNum >= totalPages);
                    paginationLast.classList.toggle('disabled', paginationPageNum >= totalPages);

                    // Update page numbers in pagination
                    const paginationItems = document.querySelectorAll('.pagination .page-item:not(#pagination-first):not(#pagination-prev):not(#pagination-next):not(#pagination-last)');
                    paginationItems.forEach(item => item.remove());

                    // Add page numbers
                    const paginationUl = document.querySelector('.pagination');
                    const nextPageItem = document.getElementById('pagination-next');

                    // Determine which page numbers to show
                    let startPage = Math.max(1, paginationPageNum - 2);
                    let endPage = Math.min(totalPages, startPage + 4);

                    // Adjust if we're near the end
                    if (endPage - startPage < 4 && startPage > 1) {
                        startPage = Math.max(1, endPage - 4);
                    }

                    for (let i = startPage; i <= endPage; i++) {
                        const li = document.createElement('li');
                        li.className = `page-item ${i === paginationPageNum ? 'active' : ''}`;

                        const a = document.createElement('a');
                        a.className = 'page-link';
                        a.href = '#';
                        a.textContent = i;

                        a.addEventListener('click', function(e) {
                            e.preventDefault();
                            paginationPageNum = i;
                            loadEvents();
                        });

                        li.appendChild(a);
                        paginationUl.insertBefore(li, nextPageItem);
                    }
                } else {
                    totalPages = Math.ceil(totalEvents / pageSize);
                }

                loadedPagesText.textContent = `${pagesLoaded} of ${totalPages} pages loaded`;

                // Check if we have more events to load
                hasMoreEvents = totalLoaded < totalEvents;

                // If search was performed, show feedback
                if (currentSearchParams.query && isInitialLoad) {
                    showSearchFeedback(currentSearchParams.query, totalEvents);
                }

                // Preload multiple pages if we have more events and we're not in pagination mode
                if (!usePagination && hasMoreEvents && page < Math.ceil(totalEvents / pageSize) - 1) {
                    // Load the next 5 pages in sequence
                    for (let i = 1; i <= 5; i++) {
                        const nextPage = page + i;
                        if (nextPage <= Math.ceil(totalEvents / pageSize) - 1 && !loadedPages.has(nextPage)) {
                            console.log(`Scheduling preload of page ${nextPage + 1}`);
                            setTimeout(() => {
                                if (!loadedPages.has(nextPage) && !isLoading) {
                                    loadPage(nextPage, false);
                                }
                            }, i * 300); // Stagger the loading to prevent overwhelming the server
                        }
                    }

                    // Also schedule a check to continue loading more pages
                    setTimeout(() => {
                        if (hasMoreEvents && !isLoading) {
                            loadMoreEvents();
                        }
                    }, 2000);
                }
            })
            .catch(error => {
                handleLoadError(error, page, isInitialLoad);
            })
            .finally(() => {
                if (isInitialLoad) {
                    showLoading(false);
                }

                // Only reset loading state if not silent
                if (!silentLoad) {
                    isLoading = false;
                }

                if (!isInitialLoad && !silentLoad) {
                    // Hide loading indicator after a short delay
                    setTimeout(() => {
                        loadingMore.style.display = 'none';
                    }, 500);
                }

                // Call the callback if provided
                if (callback) {
                    callback();
                }
            });
    }

    function handleLoadError(error, page, isInitialLoad) {
        if (error.name === 'AbortError') {
            console.warn(`Request for page ${page + 1} was aborted due to timeout`);

            if (isInitialLoad) {
                eventsTableBody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center py-5">
                            <p class="text-danger">Request timed out. Please try again with a more specific search.</p>
                        </td>
                    </tr>
                `;
            } else {
                // Implement retry logic for timeouts
                if (currentRetries < maxRetries) {
                    currentRetries++;
                    console.log(`Retry attempt ${currentRetries} of ${maxRetries} for page ${page + 1}`);

                    // Show retry message
                    loadingMore.innerHTML = `
                        <div class="alert alert-warning">
                            Request for page ${page + 1} timed out. Retrying in 3 seconds... (Attempt ${currentRetries} of ${maxRetries})
                        </div>
                    `;

                    // Wait 3 seconds before retrying
                    setTimeout(() => {
                        loadPage(page, isInitialLoad);
                    }, 3000);
                } else {
                    // Max retries reached
                    loadingMore.innerHTML = `
                        <div class="alert alert-warning">
                            Request for page ${page + 1} timed out after ${maxRetries} attempts.
                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="loadPage(${page}, ${isInitialLoad})">Try Again</button>
                        </div>
                    `;
                    currentRetries = 0;
                }
            }
        } else {
            console.error(`Error loading page ${page + 1}:`, error);

            if (isInitialLoad) {
                alert('Error loading events. See console for details.');
            } else {
                // Implement retry logic for other errors
                if (currentRetries < maxRetries) {
                    currentRetries++;
                    console.log(`Retry attempt ${currentRetries} of ${maxRetries} for page ${page + 1}`);

                    // Show retry message
                    loadingMore.innerHTML = `
                        <div class="alert alert-danger">
                            Error loading page ${page + 1}. Retrying in 3 seconds... (Attempt ${currentRetries} of ${maxRetries})
                        </div>
                    `;

                    // Wait 3 seconds before retrying
                    setTimeout(() => {
                        loadPage(page, isInitialLoad);
                    }, 3000);
                } else {
                    // Max retries reached
                    loadingMore.innerHTML = `
                        <div class="alert alert-danger">
                            Error loading page ${page + 1} after ${maxRetries} attempts.
                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="loadPage(${page}, ${isInitialLoad})">Try Again</button>
                        </div>
                    `;
                    currentRetries = 0;
                }
            }
        }
    }

    function loadMoreEvents() {
        if (isLoading || !hasMoreEvents) return;

        try {
            // Calculate the next page to load
            let nextPage = 0;

            if (loadedPages.size > 0) {
                // Get the highest loaded page
                nextPage = Math.max(...Array.from(loadedPages)) + 1;
            }

            const maxPage = Math.ceil(totalEvents / pageSize) - 1;

            if (nextPage <= maxPage) {
                console.log(`Loading next page ${nextPage} (max page: ${maxPage})`);

                // Update the progress indicators even before loading
                const totalLoaded = Array.from(loadedPages).reduce((total, p) => total + (loadedEvents[p]?.length || 0), 0);
                const showingText = document.querySelector('#showing-info .d-flex .text-white');
                if (showingText) {
                    showingText.textContent = `Showing ${totalLoaded.toLocaleString()} of ${totalEvents.toLocaleString()} events`;
                }

                // Load the page
                loadPage(nextPage);

                // Schedule loading of additional pages - more aggressive approach
                // Try to load multiple pages in parallel
                const pagesToLoad = Math.min(5, maxPage - nextPage);

                if (pagesToLoad > 0) {
                    console.log(`Scheduling loading of ${pagesToLoad} additional pages`);

                    // Load the next few pages in sequence
                    for (let i = 1; i <= pagesToLoad; i++) {
                        const pageToLoad = nextPage + i;
                        if (pageToLoad <= maxPage && !loadedPages.has(pageToLoad)) {
                            setTimeout(() => {
                                if (!isLoading && hasMoreEvents && !loadedPages.has(pageToLoad)) {
                                    console.log(`Loading additional page ${pageToLoad}`);
                                    loadPage(pageToLoad, false);
                                }
                            }, i * 200); // Stagger the loading
                        }
                    }

                    // Also schedule another batch of loading
                    setTimeout(() => {
                        if (!isLoading && hasMoreEvents) {
                            loadMoreEvents();
                        }
                    }, (pagesToLoad + 1) * 200);
                }
            } else {
                console.log('No more pages to load');
                hasMoreEvents = false;
            }
        } catch (error) {
            console.error('Error in loadMoreEvents:', error);

            // Show error message
            loadingMore.style.display = 'block';
            loadingMore.innerHTML = `
                <div class="alert alert-warning">
                    Error loading more events.
                    <button class="btn btn-sm btn-outline-primary ms-2" onclick="resetAndRetry()">Try Again</button>
                </div>
            `;
        }
    }

    function renderPage(page) {
        if (!loadedEvents[page] || loadedEvents[page].length === 0) {
            console.warn(`No events found for page ${page}`);
            return;
        }

        const events = loadedEvents[page];
        console.log(`Rendering page ${page + 1} with ${events.length} events`);

        // Update current page
        currentPage = page;

        if (usePagination) {
            // In pagination mode, we replace the visible events with just this page
            visibleEvents = [...events];
            currentEvents = [...events]; // Make sure currentEvents is updated

            // Clear the table first
            eventsTableBody.innerHTML = '';

            // Render the events for this page only
            renderEvents(events, true);

            // Scroll to top of the table
            window.scrollTo({
                top: eventsTableBody.offsetTop - 100,
                behavior: 'smooth'
            });
        } else {
            // In infinite scroll mode, we add these events to the visible events array
            visibleEvents = [...visibleEvents, ...events];

            // Render the events (append to existing)
            renderEvents(events, false);
        }

        // Update the "Load More" button visibility and progress indicators
        const totalLoaded = Array.from(loadedPages).reduce((total, p) => total + (loadedEvents[p]?.length || 0), 0);

        // Update the main text
        const showingText = document.querySelector('#showing-info .d-flex .text-white');
        showingText.textContent = `Showing ${totalLoaded.toLocaleString()} of ${totalEvents.toLocaleString()} events`;

        // Calculate and update the percentage - ensure it's accurate for large datasets
        let percentage = 0;
        if (totalEvents > 0) {
            // Calculate percentage with higher precision for large datasets
            percentage = Math.min(Math.round((totalLoaded / totalEvents) * 100), 100);

            // If we've loaded at least one page but percentage is 0, show at least 1%
            if (percentage === 0 && loadedPages.size > 0) {
                percentage = 1;
            }
        }

        // Update the progress bar
        const progressBar = document.getElementById('progress-bar');
        progressBar.style.width = `${percentage}%`;
        progressBar.setAttribute('aria-valuenow', percentage);

        // Update the percentage text
        const loadedPercentage = document.getElementById('loaded-percentage');
        loadedPercentage.textContent = `${percentage}% loaded`;

        // Update the pages loaded text
        const loadedPagesText = document.getElementById('loaded-pages');
        const pagesLoaded = Array.from(loadedPages).length;

        // Calculate total pages differently based on pagination mode
        let totalPages;
        if (usePagination) {
            // Update pagination controls
            updatePaginationControls();
        } else {
            totalPages = Math.ceil(totalEvents / pageSize);
        }

        if (loadedPagesText) {
            loadedPagesText.textContent = `${pagesLoaded} of ${totalPages} pages loaded`;
        }

        // Make sure loadMoreBtn is initialized
        if (!loadMoreBtn) {
            loadMoreBtn = document.getElementById('load-more-btn');
        }

        if (totalLoaded < totalEvents && loadMoreBtn) {
            loadMoreBtn.style.display = 'block';
        } else if (loadMoreBtn) {
            loadMoreBtn.style.display = 'none';

            // Show completion message
            if (loadingMore) {
                loadingMore.style.display = 'block';
                loadingMore.innerHTML = `
                    <div class="alert alert-success">
                        All ${totalEvents.toLocaleString()} events loaded successfully!
                    </div>
                `;
            }

            // Update progress to 100%
            if (progressBar) {
                progressBar.style.width = '100%';
                progressBar.setAttribute('aria-valuenow', 100);
            }

            if (loadedPercentage) {
                loadedPercentage.textContent = '100% loaded';
            }

            // Hide the message after 3 seconds
            setTimeout(() => {
                if (loadingMore) {
                    loadingMore.style.display = 'none';
                    loadingMore.innerHTML = '';
                }
            }, 3000);
        }
    }







    // Function to render all loaded events
    function renderAllEvents() {
        console.log("Rendering all loaded events");

        // Combine all loaded events into a single array
        const allEvents = [];
        const sortedPages = Array.from(loadedPages).sort((a, b) => a - b);

        for (const page of sortedPages) {
            if (loadedEvents[page]) {
                allEvents.push(...loadedEvents[page]);
            }
        }

        console.log(`Total events to render: ${allEvents.length}`);

        // Update the current events array
        currentEvents = [...allEvents];
        visibleEvents = [...allEvents];

        // Clear the table
        eventsTableBody.innerHTML = '';

        // Render the events
        renderEvents(allEvents, true);

        // Update the showing info
        const showingText = document.querySelector('#showing-info .d-flex .text-white');
        if (showingText) {
            showingText.textContent = `Showing ${allEvents.length.toLocaleString()} of ${totalEvents.toLocaleString()} events`;
        }

        // Update progress to 100%
        const progressBar = document.getElementById('progress-bar');
        progressBar.style.width = '100%';
        progressBar.setAttribute('aria-valuenow', 100);

        const loadedPercentage = document.getElementById('loaded-percentage');
        loadedPercentage.textContent = '100% loaded';
    }

    // Make renderEvents globally available
    window.renderEvents = function(events, isFirstLoad = true) {
        // Auto-detect columns if enabled
        if (autoDetectColumns && isFirstLoad && events.length > 0) {
            detectColumns(events);
        }

        // First load initialization
        if (isFirstLoad) {
            // Check if any columns are visible
            const hasVisibleColumns = availableColumns.some(column => column.visible);

            // If no columns are visible, make the default columns visible
            if (!hasVisibleColumns) {
                availableColumns.forEach(column => {
                    if (['timestamp', 'event_type', 'source', 'file_name', 'artifact_name', 'description'].includes(column.id)) {
                        column.visible = true;
                    }
                });
            }

            // Update table headers based on visible columns
            updateTableHeaders();

            // Clear the table if this is a first load
            eventsTableBody.innerHTML = '';

            // In pagination mode, we replace the current events
            if (usePagination) {
                currentEvents = [...events];
            }
        } else if (usePagination) {
            // In pagination mode, we replace the current events even if not first load
            currentEvents = [...events];
            eventsTableBody.innerHTML = '';
        }

        // Filter events if filters are active
        const filteredEvents = filterEvents(events);

        // PERFORMANCE FIX: For very large datasets, automatically enable pagination
        const LARGE_DATASET_THRESHOLD = 1000; // Threshold for auto-enabling pagination
        let eventsToRender = filteredEvents;

        if (!usePagination && filteredEvents.length > LARGE_DATASET_THRESHOLD) {
            console.log(`Large dataset detected (${filteredEvents.length} events). Auto-enabling pagination for optimal performance.`);

            // Automatically enable pagination for large datasets
            usePagination = true;
            paginationToggle.checked = true;
            paginationControls.style.display = 'block';

            // Calculate pagination
            totalEvents = filteredEvents.length;
            totalPaginationPages = Math.ceil(totalEvents / RECORDS_PER_PAGE);
            paginationPageNum = 1;

            // Show only the first page of events
            eventsToRender = filteredEvents.slice(0, RECORDS_PER_PAGE);

            // Update pagination controls
            updatePaginationControls();

            // Show info message about auto-pagination
            showAutoPaginationMessage(filteredEvents.length);
        }

        // Populate table view with performance optimization
        const fragment = document.createDocumentFragment();

        eventsToRender.forEach((event, index) => {
            const row = document.createElement('tr');
            row.className = 'event-row';
            row.dataset.eventId = event.id;
            row.dataset.page = currentPage;

            // Create cells based on visible columns
            let cellsHtml = '';

            availableColumns.forEach(column => {
                if (column.visible) {
                    let cellContent = '';
                    let cellValue = '';

                    if (column.id === 'timestamp') {
                        // Format timestamp directly with UTC+3 timezone
                        const date = new Date(event.timestamp);
                        if (!isNaN(date.getTime())) {
                            try {
                                // Format the date in UTC+3 timezone
                                const options = {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    second: '2-digit',
                                    hour12: false,
                                    timeZone: 'Europe/Moscow' // UTC+3 timezone
                                };

                                // Format the date with the specified options
                                const formattedDate = date.toLocaleString('en-US', options);

                                // Add milliseconds
                                const formatted = formattedDate + '.' + String(date.getMilliseconds()).padStart(3, '0') + ' (UTC+3)';
                                cellContent = formatted;
                                cellValue = formatted;
                            } catch (error) {
                                // Fallback to standard formatting if timezone formatting fails
                                const formatted = date.toLocaleString() + '.' + String(date.getMilliseconds()).padStart(3, '0');
                                cellContent = formatted;
                                cellValue = formatted;
                            }
                        } else {
                            cellContent = event.timestamp || 'N/A';
                            cellValue = event.timestamp || 'N/A';
                        }
                    } else if (column.id === 'event_type') {
                        cellContent = `<span class="event-type-badge event-type-${getEventTypeClass(event.event_type)}">${event.event_type}</span>`;
                        cellValue = event.event_type;
                    } else if (column.id.startsWith('details.')) {
                        // Handle detail fields
                        const fieldPath = column.id.substring(8); // Remove 'details.' prefix

                        // Parse details if it's a string
                        let detailsObj = event.details;
                        if (typeof event.details === 'string') {
                            try {
                                detailsObj = JSON.parse(event.details);
                            } catch (e) {
                                console.error('Failed to parse details JSON for field', fieldPath, ':', e);
                                detailsObj = {};
                            }
                        }

                        const fieldValue = getNestedValue(detailsObj, fieldPath);

                        if (fieldValue !== undefined && fieldValue !== null) {
                            if (typeof fieldValue === 'object') {
                                cellContent = JSON.stringify(fieldValue);
                                cellValue = JSON.stringify(fieldValue);
                            } else {
                                cellContent = String(fieldValue);
                                cellValue = String(fieldValue);
                            }
                        } else {
                            cellContent = 'N/A';
                            cellValue = '';
                        }
                    } else {
                        const fieldValue = event[column.id];

                        if (fieldValue === undefined || fieldValue === null) {
                            cellContent = 'N/A';
                            cellValue = '';
                        } else if (typeof fieldValue === 'object') {
                            // Handle objects properly to avoid [object Object]
                            cellContent = JSON.stringify(fieldValue);
                            cellValue = JSON.stringify(fieldValue);
                        } else {
                            cellContent = fieldValue;
                            cellValue = fieldValue;
                        }
                    }

                    // Escape HTML attributes to prevent syntax errors
                    const escapedCellValue = String(cellValue).replace(/"/g, '&quot;').replace(/'/g, '&#39;');
                    cellsHtml += `<td data-value="${escapedCellValue}">${cellContent}</td>`;
                }
            });

            // Add actions column
            cellsHtml += `
                <td>
                    <button class="btn btn-sm btn-primary view-details-btn">Details</button>
                </td>
            `;

            row.innerHTML = cellsHtml;

            // Add event listener for the details button
            row.querySelector('.view-details-btn').addEventListener('click', function(e) {
                e.stopPropagation(); // Prevent row click event from firing
                showEventDetailsInPanel(event);
            });

            // Make the entire row clickable to show details
            row.addEventListener('click', function() {
                showEventDetailsInPanel(event);

                // Highlight the selected row
                const rows = eventsTableBody.querySelectorAll('tr');
                rows.forEach(r => {
                    r.classList.remove('table-primary');
                    r.classList.remove('keyboard-focus');
                });
                row.classList.add('table-primary');

                // Update the selected row index for keyboard navigation
                const allRows = Array.from(eventsTableBody.querySelectorAll('tr.event-row'));
                selectedRowIndex = allRows.indexOf(row);
            });

            // Add to fragment instead of directly to DOM
            fragment.appendChild(row);
        });

        // Append all rows at once for better performance
        eventsTableBody.appendChild(fragment);

        // Extract detail fields from the first few events for column selection
        if (isFirstLoad && events.length > 0) {
            updateDetailFields(events.slice(0, 10));
        }
    }; // Close the window.renderEvents function

    function showAutoPaginationMessage(totalEvents) {
        // Remove any existing message
        const existingMessage = document.getElementById('auto-pagination-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // Create info message
        const messageDiv = document.createElement('div');
        messageDiv.id = 'auto-pagination-message';
        messageDiv.className = 'alert alert-info alert-dismissible fade show mt-3';
        messageDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi bi-info-circle-fill me-2"></i>
                <div class="flex-grow-1">
                    <strong>Large Dataset Loaded Successfully!</strong><br>
                    <small>All ${totalEvents.toLocaleString()} events are available. Pagination has been automatically enabled for optimal performance.
                    Use the navigation controls below to browse through all events.</small>
                </div>
                <div class="ms-3">
                    <button type="button" class="btn btn-sm btn-outline-primary" id="increase-page-size-btn">
                        Show More Per Page
                    </button>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // Insert message above the table
        const tableCard = document.querySelector('#table-view .card-body');
        if (tableCard) {
            tableCard.insertBefore(messageDiv, tableCard.firstChild);
        }

        // Add event listener for page size increase button
        const increasePageSizeBtn = document.getElementById('increase-page-size-btn');
        if (increasePageSizeBtn) {
            increasePageSizeBtn.addEventListener('click', function() {
                // Increase page size for users who want to see more events per page
                RECORDS_PER_PAGE = Math.min(RECORDS_PER_PAGE * 2, 500); // Max 500 per page

                // Update the records per page display
                const recordsPerPageSpan = document.getElementById('records-per-page');
                if (recordsPerPageSpan) {
                    recordsPerPageSpan.textContent = RECORDS_PER_PAGE;
                }

                // Recalculate pagination
                totalPaginationPages = Math.ceil(totalEvents / RECORDS_PER_PAGE);
                paginationPageNum = 1;

                // Reload current page with new page size
                loadPaginatedEvents();

                // Update button text
                increasePageSizeBtn.textContent = `Show ${RECORDS_PER_PAGE} Per Page`;

                if (RECORDS_PER_PAGE >= 500) {
                    increasePageSizeBtn.disabled = true;
                    increasePageSizeBtn.textContent = 'Max Page Size Reached';
                }
            });
        }
    }

    function filterEvents(events) {
        // If no filters are active, return all events
        if (Object.keys(columnFilters).length === 0) {
            console.log('No filters active, returning all events');
            return events;
        }

        console.log(`🔍 Filtering ${events.length} events with filters:`, columnFilters);

        // Filter events based on active filters
        const filteredEvents = events.filter(event => {
            // Check each filter - ALL filters must pass (AND logic)
            for (const columnId in columnFilters) {
                const allowedValues = columnFilters[columnId];

                // Skip if no values are allowed (empty filter)
                if (!allowedValues || allowedValues.length === 0) {
                    continue;
                }

                let value;

                if (columnId === 'timestamp') {
                    // Format timestamp directly with UTC+3 timezone
                    const date = new Date(event.timestamp);
                    if (!isNaN(date.getTime())) {
                        try {
                            // Format the date in UTC+3 timezone
                            const options = {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit',
                                hour12: false,
                                timeZone: 'Europe/Moscow' // UTC+3 timezone
                            };

                            // Format the date with the specified options
                            const formattedDate = date.toLocaleString('en-US', options);

                            // Add milliseconds
                            value = formattedDate + '.' + String(date.getMilliseconds()).padStart(3, '0') + ' (UTC+3)';
                        } catch (error) {
                            // Fallback to standard formatting if timezone formatting fails
                            value = date.toLocaleString() + '.' + String(date.getMilliseconds()).padStart(3, '0');
                        }
                    } else {
                        value = event.timestamp || 'N/A';
                    }
                } else if (columnId.startsWith('details.')) {
                    const fieldPath = columnId.substring(8);

                    // Parse details if it's a string
                    let detailsObj = event.details;
                    if (typeof event.details === 'string') {
                        try {
                            detailsObj = JSON.parse(event.details);
                        } catch (e) {
                            detailsObj = {};
                        }
                    }

                    const fieldValue = getNestedValue(detailsObj, fieldPath);

                    if (fieldValue === undefined) {
                        value = '';
                    } else if (typeof fieldValue === 'object') {
                        value = JSON.stringify(fieldValue);
                    } else {
                        value = String(fieldValue);
                    }
                } else {
                    const fieldValue = event[columnId];

                    if (fieldValue === undefined || fieldValue === null) {
                        value = '';
                    } else if (typeof fieldValue === 'object') {
                        value = JSON.stringify(fieldValue);
                    } else {
                        value = String(fieldValue);
                    }
                }

                // If the value is not in the allowed values, exclude this event
                if (!allowedValues.includes(value)) {
                    return false;
                }
            }

            // If all filters pass, include this event
            return true;
        });

        console.log(`🔍 Filtered from ${events.length} to ${filteredEvents.length} events`);
        return filteredEvents;
    }

    function detectColumns(events) {
        console.log('detectColumns called with', events.length, 'events');
        if (events.length === 0) return;

        // Save existing column visibility settings and constant flags
        const visibilitySettings = {};
        const constantFlags = {};
        availableColumns.forEach(column => {
            visibilitySettings[column.id] = column.visible;
            if (column.constant) {
                constantFlags[column.id] = true;
            }
        });

        console.log('Existing visibility settings:', visibilitySettings);

        // Start with the basic columns
        const baseColumns = [
            { id: 'timestamp', label: 'Timestamp', visible: visibilitySettings['timestamp'] !== undefined ? visibilitySettings['timestamp'] : true },
            { id: 'event_type', label: 'Event Type', visible: visibilitySettings['event_type'] !== undefined ? visibilitySettings['event_type'] : true },
            { id: 'source', label: 'Source', visible: visibilitySettings['source'] !== undefined ? visibilitySettings['source'] : true },
            { id: 'file_name', label: 'File Name', visible: visibilitySettings['file_name'] !== undefined ? visibilitySettings['file_name'] : true },
            { id: 'artifact_name', label: 'Artifact Name', visible: true, constant: true }, // Always visible and constant
            { id: 'description', label: 'Description', visible: visibilitySettings['description'] !== undefined ? visibilitySettings['description'] : true }
        ];

        // Collect all top-level fields from events
        const allFields = new Set();

        events.slice(0, 10).forEach(event => {
            Object.keys(event).forEach(key => {
                // Skip id, timestamp, event_type, source, description, and details
                if (!['id', 'timestamp', 'event_type', 'source', 'description', 'details'].includes(key)) {
                    allFields.add(key);
                }
            });
        });

        // Create columns for all fields
        const fieldColumns = Array.from(allFields).map(field => {
            return {
                id: field,
                label: field.charAt(0).toUpperCase() + field.slice(1).replace(/_/g, ' '),
                visible: visibilitySettings[field] !== undefined ? visibilitySettings[field] : false
            };
        });

        // Combine base columns with field columns
        availableColumns = [...baseColumns, ...fieldColumns];

        console.log('Base columns:', baseColumns);
        console.log('Field columns:', fieldColumns);

        // Also add detail fields as columns
        const detailFieldsSet = new Set();

        events.slice(0, 10).forEach(event => {
            let detailsObj = event.details;

            // Parse details if it's a string
            if (typeof event.details === 'string') {
                try {
                    detailsObj = JSON.parse(event.details);
                } catch (e) {
                    console.error('Failed to parse details JSON during column detection:', e);
                    detailsObj = null;
                }
            }

            if (detailsObj && typeof detailsObj === 'object') {
                extractDetailFields(detailsObj, '', detailFieldsSet);
            }
        });

        // Add the most common detail fields (up to 5)
        const topDetailFields = Array.from(detailFieldsSet).slice(0, 5);

        topDetailFields.forEach(field => {
            const label = field.split('.').pop();
            const columnId = `details.${field}`;
            availableColumns.push({
                id: columnId,
                label: label.charAt(0).toUpperCase() + label.slice(1).replace(/_/g, ' '),
                visible: visibilitySettings[columnId] !== undefined ? visibilitySettings[columnId] : false
            });
        });

        console.log('Final availableColumns:', availableColumns);
        console.log('Total columns detected:', availableColumns.length);
    }

    function updateTableHeaders() {
        // Clear existing headers
        eventsTableHead.innerHTML = '';

        // Create header row
        const headerRow = document.createElement('tr');

        // Add cells for visible columns
        availableColumns.forEach((column, index) => {
            if (column.visible) {
                const th = document.createElement('th');
                th.style.width = column.width || '150px';
                th.dataset.columnId = column.id;
                th.dataset.columnIndex = index;

                // Create header content with drag handle and filter icon
                const headerContent = document.createElement('div');
                headerContent.className = 'column-header';
                headerContent.draggable = true;

                // Add drag handle
                const dragHandle = document.createElement('i');
                dragHandle.className = 'bi bi-grip-vertical drag-handle';
                dragHandle.title = 'Drag to reorder';
                headerContent.appendChild(dragHandle);

                const labelSpan = document.createElement('span');
                labelSpan.textContent = column.label;
                labelSpan.style.flex = '1';
                headerContent.appendChild(labelSpan);

                // Add filter icon
                const filterIcon = document.createElement('i');
                filterIcon.className = 'bi bi-funnel filter-icon';
                if (columnFilters[column.id]) {
                    filterIcon.classList.add('filter-active');
                }
                filterIcon.setAttribute('data-column-id', column.id);
                filterIcon.title = 'Filter column';
                headerContent.appendChild(filterIcon);

                // Add resize handle
                const resizeHandle = document.createElement('div');
                resizeHandle.className = 'column-resize-handle';
                resizeHandle.title = 'Resize column';

                th.appendChild(headerContent);
                th.appendChild(resizeHandle);
                headerRow.appendChild(th);

                // Add event listeners
                setupColumnEventListeners(th, headerContent, filterIcon, resizeHandle, column, index);
            }
        });

        // Add actions column
        const actionsHeader = document.createElement('th');
        actionsHeader.textContent = 'Actions';
        actionsHeader.style.width = '120px';
        headerRow.appendChild(actionsHeader);

        eventsTableHead.appendChild(headerRow);
    }

    function setupColumnEventListeners(th, headerContent, filterIcon, resizeHandle, column, index) {
        // Filter icon click
        filterIcon.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleFilterDropdown(column.id, e.target);
        });

        // Column resizing
        let isResizing = false;
        let startX = 0;
        let startWidth = 0;

        resizeHandle.addEventListener('mousedown', function(e) {
            e.preventDefault();
            e.stopPropagation();
            isResizing = true;
            startX = e.clientX;
            startWidth = parseInt(document.defaultView.getComputedStyle(th).width, 10);
            document.addEventListener('mousemove', handleResize);
            document.addEventListener('mouseup', stopResize);
            document.body.style.cursor = 'col-resize';
        });

        function handleResize(e) {
            if (!isResizing) return;
            const width = startWidth + e.clientX - startX;
            const minWidth = 100;
            const maxWidth = 500;
            const newWidth = Math.max(minWidth, Math.min(maxWidth, width));
            th.style.width = newWidth + 'px';
            column.width = newWidth + 'px';
        }

        function stopResize() {
            isResizing = false;
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
            document.body.style.cursor = '';
        }

        // Column drag and drop
        let draggedColumn = null;

        headerContent.addEventListener('dragstart', function(e) {
            if (e.target.classList.contains('filter-icon') || e.target.classList.contains('column-resize-handle')) {
                e.preventDefault();
                return;
            }
            draggedColumn = { element: th, column: column, index: index };
            headerContent.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', th.outerHTML);
        });

        headerContent.addEventListener('dragend', function(e) {
            headerContent.classList.remove('dragging');
            draggedColumn = null;
            // Remove all drop zone indicators
            document.querySelectorAll('.drop-zone').forEach(el => {
                el.classList.remove('drag-over');
            });
        });

        th.addEventListener('dragover', function(e) {
            if (draggedColumn && draggedColumn.element !== th) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
                th.classList.add('drop-zone', 'drag-over');
            }
        });

        th.addEventListener('dragleave', function(e) {
            th.classList.remove('drag-over');
        });

        th.addEventListener('drop', function(e) {
            e.preventDefault();
            if (draggedColumn && draggedColumn.element !== th) {
                const targetIndex = parseInt(th.dataset.columnIndex);
                const sourceIndex = draggedColumn.index;

                // Reorder columns in the availableColumns array
                reorderColumns(sourceIndex, targetIndex);

                // Update table headers and re-render
                updateTableHeaders();
                if (currentEvents.length > 0) {
                    renderEvents(currentEvents, false);
                }
            }
            th.classList.remove('drop-zone', 'drag-over');
        });
    }

    function reorderColumns(fromIndex, toIndex) {
        // Find the actual visible column indices
        const visibleColumns = availableColumns.filter(col => col.visible);
        const fromColumn = visibleColumns[fromIndex];
        const toColumn = visibleColumns[toIndex];

        if (!fromColumn || !toColumn) return;

        // Find their positions in the full availableColumns array
        const fromFullIndex = availableColumns.findIndex(col => col.id === fromColumn.id);
        const toFullIndex = availableColumns.findIndex(col => col.id === toColumn.id);

        // Remove the column from its current position
        const [movedColumn] = availableColumns.splice(fromFullIndex, 1);

        // Insert it at the new position
        const adjustedToIndex = fromFullIndex < toFullIndex ? toFullIndex - 1 : toFullIndex;
        availableColumns.splice(adjustedToIndex, 0, movedColumn);

        console.log(`Moved column "${fromColumn.label}" from position ${fromIndex} to ${toIndex}`);
    }

    function getNestedValue(obj, path) {
        if (!obj) return undefined;

        const keys = path.split('.');
        let value = obj;

        for (const key of keys) {
            if (value === null || value === undefined || typeof value !== 'object') {
                return undefined;
            }
            value = value[key];
        }

        return value;
    }

    function getEventTypeClass(eventType) {
        if (!eventType) return 'info';

        const type = eventType.toLowerCase();
        if (type.includes('error') || type.includes('fail')) {
            return 'error';
        } else if (type.includes('warn')) {
            return 'warning';
        } else if (type.includes('success') || type.includes('complete')) {
            return 'success';
        } else {
            return 'info';
        }
    }

    function showEventDetails(event) {
        // Get the current search query for highlighting
        const searchInput = document.getElementById('search-input');
        let searchQuery = searchInput ? searchInput.value.trim() : '';

        // Fallback to currentSearchParams if input is empty but we're in search mode
        if (!searchQuery && isSearchMode && currentSearchParams.query) {
            searchQuery = currentSearchParams.query;
        }

        // Format the event details as HTML - only show raw JSON
        let detailsHtml = `
            ${searchQuery ? `<div class="mb-3 alert alert-warning">
                <strong>Highlighting:</strong> "${searchQuery}"
            </div>` : ''}
            <div class="mb-3">
                <h6>Raw JSON</h6>
                <pre style="white-space: pre-wrap; word-wrap: break-word;">${formatJSON(event, searchQuery)}</pre>
            </div>
        `;

        // Update modal content and show it
        eventDetailsContent.innerHTML = detailsHtml;
        eventDetailsModal.show();

        // If there's a search query, scroll to the first highlighted match
        if (searchQuery) {
            setTimeout(() => {
                const firstHighlight = eventDetailsContent.querySelector('.keyword-highlight');
                if (firstHighlight) {
                    firstHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }, 100);
        }
    }

    function showEventDetailsInPanel(event) {
        // Store the selected event
        selectedEvent = event;

        // Use requestAnimationFrame for better performance
        requestAnimationFrame(() => {
            // Get the current search query for highlighting
            const searchInput = document.getElementById('search-input');
            let searchQuery = searchInput ? searchInput.value.trim() : '';

            // Fallback to currentSearchParams if input is empty but we're in search mode
            if (!searchQuery && isSearchMode && currentSearchParams.query) {
                searchQuery = currentSearchParams.query;
                console.log('🎨 Using fallback search query from currentSearchParams:', searchQuery);
            }

            console.log('🎨 showEventDetailsInPanel - searchQuery:', searchQuery);
            console.log('🎨 showEventDetailsInPanel - isSearchMode:', isSearchMode);
            console.log('🎨 showEventDetailsInPanel - currentSearchParams:', currentSearchParams);

            // Format the event details as HTML - only show the raw JSON
            const formattedJSON = formatJSON(event, searchQuery);
            console.log('🎨 formatJSON result length:', formattedJSON.length);
            console.log('🎨 formatJSON contains highlights:', formattedJSON.includes('keyword-highlight'));

            let detailsHtml = `
                <div class="mb-4 p-3 rounded" style="background-color: rgba(25, 135, 84, 0.1);">
                    <button class="btn btn-sm ${taggedEvents.has(event.id) ? 'btn-success' : 'btn-outline-success'}" id="tag-event-btn">
                        <i class="bi bi-tag${taggedEvents.has(event.id) ? '-fill' : ''}"></i>
                        ${taggedEvents.has(event.id) ? 'Remove Tag' : 'Tag Event (m)'}
                    </button>
                    ${searchQuery ? `<span class="ms-3 badge bg-warning text-dark">Highlighting: "${searchQuery}"</span>` : ''}
                </div>

                <div class="mb-4">
                    <h6>Raw JSON</h6>
                    <pre class="p-3 rounded" style="background-color: rgba(108, 117, 125, 0.1); max-height: 80vh; overflow: auto; white-space: pre-wrap; word-wrap: break-word;">${formattedJSON}</pre>
                </div>
            `;

            // Update panel content and show it
            eventDetailsPanel.innerHTML = detailsHtml;

            // Show the panel and close button
            detailsPlaceholder.style.display = 'none';
            eventDetailsPanel.style.display = 'block';
            closeDetailsBtn.style.display = 'block';

            // Highlight the selected row in the table
            const rows = eventsTableBody.querySelectorAll('tr');
            rows.forEach(row => {
                row.classList.remove('table-primary');
                if (row.dataset.eventId === event.id) {
                    row.classList.add('table-primary');
                }
            });

            // Add event listener for the tag button
            const tagButton = document.getElementById('tag-event-btn');
            if (tagButton) {
                tagButton.addEventListener('click', function() {
                    // Find the row for this event
                    const row = document.querySelector(`tr[data-event-id="${event.id}"]`);
                    toggleEventTag(event, row);

                    // Update the button appearance
                    this.className = `btn btn-sm ${taggedEvents.has(event.id) ? 'btn-success' : 'btn-outline-success'}`;
                    this.innerHTML = `
                        <i class="bi bi-tag${taggedEvents.has(event.id) ? '-fill' : ''}"></i>
                        ${taggedEvents.has(event.id) ? 'Remove Tag' : 'Tag Event (m)'}
                    `;
                });
            }

            // If there's a search query, scroll to the first highlighted match
            if (searchQuery) {
                console.log('🎨 Attempting autoscroll for search query:', searchQuery);
                setTimeout(() => {
                    const highlights = eventDetailsPanel.querySelectorAll('.keyword-highlight');
                    console.log('🎨 Found', highlights.length, 'highlights for autoscroll');

                    if (highlights.length > 0) {
                        const firstHighlight = highlights[0];
                        console.log('🎨 Scrolling to first highlight:', firstHighlight.textContent);

                        // Scroll the highlight into view
                        firstHighlight.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'nearest'
                        });

                        // Also add a visual pulse effect to make it more obvious
                        firstHighlight.style.animation = 'pulse 2s ease-in-out';
                        setTimeout(() => {
                            firstHighlight.style.animation = '';
                        }, 2000);
                    } else {
                        console.log('🎨 No highlights found for autoscroll');
                    }
                }, 200); // Increased timeout to ensure DOM is fully updated
            }
        });
    }

    function hideEventDetails() {
        // Clear the selected event
        selectedEvent = null;

        // Hide the panel and close button
        detailsPlaceholder.style.display = 'block';
        eventDetailsPanel.style.display = 'none';
        closeDetailsBtn.style.display = 'none';

        // Remove highlighting from all rows
        const rows = eventsTableBody.querySelectorAll('tr');
        rows.forEach(row => {
            row.classList.remove('table-primary');
        });
    }

    // We no longer need the formatDetailsTree function since we're only showing raw JSON
    function formatDetailsTree(details) {
        // Just return the JSON string for simplicity
        return formatJSON(details);
    }

    function formatJSON(obj, highlightKeyword = null) {
        // Create a deep copy of the object to avoid modifying the original
        const objCopy = JSON.parse(JSON.stringify(obj));

        // Format any timestamp fields directly in the object
        if (objCopy && typeof objCopy === 'object') {
            // Format timestamp field if it exists
            if (objCopy.timestamp) {
                const date = new Date(objCopy.timestamp);
                if (!isNaN(date.getTime())) {
                    try {
                        // Format the date in UTC+3 timezone
                        const options = {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            hour12: false,
                            timeZone: 'Europe/Moscow' // UTC+3 timezone
                        };

                        // Format the date with the specified options
                        const formattedDate = date.toLocaleString('en-US', options);

                        // Add milliseconds
                        objCopy.timestamp_formatted = formattedDate + '.' + String(date.getMilliseconds()).padStart(3, '0') + ' (UTC+3)';
                    } catch (error) {
                        // Fallback to standard formatting if timezone formatting fails
                        objCopy.timestamp_formatted = date.toLocaleString() + '.' + String(date.getMilliseconds()).padStart(3, '0');
                    }
                }
            }
        }

        // Convert the object to a formatted JSON string
        let jsonString = JSON.stringify(objCopy, null, 2)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;');

        // Apply syntax highlighting
        jsonString = jsonString.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function(match) {
            let cls = 'json-number';
            if (/^"/.test(match)) {
                if (/:$/.test(match)) {
                    cls = 'json-key';
                } else {
                    cls = 'json-string';
                }
            } else if (/true|false/.test(match)) {
                cls = 'json-boolean';
            } else if (/null/.test(match)) {
                cls = 'json-null';
            }
            return '<span class="' + cls + '">' + match + '</span>';
        });

        // If a keyword is provided, highlight it
        if (highlightKeyword && highlightKeyword.trim() !== '') {
            console.log('🎨 formatJSON - highlighting keyword:', highlightKeyword);

            // Prepare the keyword for highlighting
            const keyword = highlightKeyword.trim();

            // Handle special cases for regex and wildcard searches
            let searchPattern;
            let isRegex = false;

            // Check if it's a regex pattern (enclosed in slashes)
            if (keyword.startsWith('/') && keyword.endsWith('/') && keyword.length > 2) {
                try {
                    // Extract the pattern without the slashes
                    searchPattern = keyword.substring(1, keyword.length - 1);
                    isRegex = true;
                } catch (e) {
                    console.error('Invalid regex pattern:', e);
                    searchPattern = escapeRegExp(keyword);
                }
            }
            // Check if it's a wildcard pattern (contains %)
            else if (keyword.includes('%')) {
                // Convert SQL LIKE wildcards (%) to regex wildcards (.*)
                searchPattern = escapeRegExp(keyword).replace(/%/g, '.*');
                isRegex = true;
            }
            // Standard search
            else {
                searchPattern = escapeRegExp(keyword);
            }

            // Create a regex for the search pattern
            // Use case-insensitive search by default
            const regex = isRegex ? new RegExp(searchPattern, 'gi') : new RegExp(searchPattern, 'gi');

            // Function to highlight matches
            const highlightMatches = (text) => {
                // Reset regex lastIndex to ensure we find all matches
                regex.lastIndex = 0;
                const result = text.replace(regex, match =>
                    `<span class="keyword-highlight">${match}</span>`
                );
                if (result !== text) {
                    console.log('🎨 Found matches in text:', text.substring(0, 100) + '...');
                }
                return result;
            };

            console.log('🎨 Search pattern:', searchPattern);
            console.log('🎨 Is regex:', isRegex);
            console.log('🎨 JSON string length before highlighting:', jsonString.length);

            // Apply highlighting to the JSON string
            // We need to be careful not to break the HTML tags we've already added

            // Split the string by HTML tags
            const parts = jsonString.split(/(<\/?span[^>]*>)/);
            console.log('🎨 Split into', parts.length, 'parts');

            let highlightCount = 0;
            // Process each part
            for (let i = 0; i < parts.length; i++) {
                // Skip HTML tags
                if (parts[i].startsWith('<')) continue;

                // Apply highlighting to text content
                const originalPart = parts[i];
                parts[i] = highlightMatches(parts[i]);
                if (parts[i] !== originalPart) {
                    highlightCount++;
                }
            }

            console.log('🎨 Applied highlighting to', highlightCount, 'parts');

            // Rejoin the parts
            jsonString = parts.join('');
            console.log('🎨 Final JSON string contains highlights:', jsonString.includes('keyword-highlight'));
        }

        return jsonString;
    }

    // Helper function to escape special characters in a string for use in a regex
    function escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
    }

    function updatePagination() {
        const start = currentPage * pageSize + 1;
        const end = Math.min((currentPage + 1) * pageSize, totalEvents);

        showingInfo.textContent = `Showing ${start}-${end} of ${totalEvents} events`;

        // Update button states
        prevPageBtn.disabled = currentPage === 0;
        nextPageBtn.disabled = end >= totalEvents;
    }

    function showLoading(show) {
        // Make the loading spinner non-blocking
        if (show) {
            loadingSpinner.style.display = 'flex';
            loadingSpinner.classList.add('non-blocking');
            // Ensure the body is not in a loading state that blocks interaction
            document.body.classList.remove('loading');
        } else {
            loadingSpinner.style.display = 'none';
            loadingSpinner.classList.remove('non-blocking');
        }
    }

    function showError(message) {
        console.error('Error:', message);

        // Show error in the table if no events are loaded
        if (eventsTableBody) {
            eventsTableBody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center py-5">
                        <div class="alert alert-danger">
                            <strong>Error:</strong> ${message}
                        </div>
                    </td>
                </tr>
            `;
        }

        // Also show an alert for immediate user feedback
        alert(`Error: ${message}`);
    }

    // Function to show a loading indicator with a message
    function showLoadingIndicator(message) {
        // Show the loading spinner
        showLoading(true);

        // Show a message in the table
        eventsTableBody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">${message}</p>
                    <p class="text-muted">This may take a moment for large datasets</p>
                </td>
            </tr>
        `;
    }

    function showSearchFeedback(query, totalResults) {
        // Create or update search feedback element
        let searchFeedback = document.getElementById('search-feedback');

        if (!searchFeedback) {
            searchFeedback = document.createElement('div');
            searchFeedback.id = 'search-feedback';
            searchFeedback.className = 'alert alert-info mt-3';

            // Insert after the search form
            const container = document.querySelector('.container-fluid');
            container.insertBefore(searchFeedback, eventsTableContainer);
        }

        // Format the message based on the number of results
        let resultMessage = '';
        const safeTotal = totalResults || 0;

        if (safeTotal === 0) {
            resultMessage = `<strong>No results found</strong> for: "${query}"`;
            searchFeedback.className = 'alert alert-warning mt-3';
        } else if (safeTotal === 1) {
            resultMessage = `<strong>1 result found</strong> for: "${query}"`;
            searchFeedback.className = 'alert alert-success mt-3';
        } else {
            resultMessage = `<strong>${safeTotal.toLocaleString()} results found</strong> for: "${query}"`;
            searchFeedback.className = 'alert alert-success mt-3';
        }

        searchFeedback.innerHTML = `
            ${resultMessage}
            <button type="button" class="btn-close float-end" aria-label="Close" onclick="clearSearchFeedback()"></button>
        `;

        searchFeedback.style.display = 'block';
    }

    // Make clearSearchFeedback globally accessible
    window.clearSearchFeedback = function() {
        const searchFeedback = document.getElementById('search-feedback');
        if (searchFeedback) {
            searchFeedback.style.display = 'none';
        }

        // Also clear the search input if it exists
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.value = '';

            // Hide the clear button
            const clearBtn = searchInput.parentNode.querySelector('button');
            if (clearBtn) {
                clearBtn.style.display = 'none';
            }
        }

        // Reset search options to defaults if they exist
        const regexCheckbox = document.getElementById('regex-search-checkbox');
        const caseSensitiveCheckbox = document.getElementById('case-sensitive-checkbox');
        const searchFieldSelect = document.getElementById('search-field');

        if (regexCheckbox) regexCheckbox.checked = false;
        if (caseSensitiveCheckbox) caseSensitiveCheckbox.checked = false;
        if (searchFieldSelect) searchFieldSelect.value = 'all';

        // Update search options object
        if (typeof searchOptions !== 'undefined') {
            searchOptions.regex = false;
            searchOptions.caseSensitive = false;
            searchOptions.field = 'all';
        }

        // Reset and reload events
        resetInfiniteScroll();
        loadEvents();
    };

    function clearSearchFeedback() {
        window.clearSearchFeedback();

        // If we have a selected event, refresh its details to remove highlighting
        if (selectedEvent) {
            showEventDetailsInPanel(selectedEvent);
        }
    }

    function loadSampleData() {
        try {
            if (typeof showLoading === 'function') {
                showLoading(true);
            }
        } catch (e) {
            console.log('showLoading not available yet');
        }

        fetch('/api/load-sample')
            .then(response => response.json())
            .then(data => {
                if (data.status) {
                    alert(`Success: ${data.message}`);
                    // Reload events
                    loadEvents();
                } else {
                    alert(`Error: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error loading sample data:', error);
                alert('Error loading sample data. See console for details.');
            })
            .finally(() => {
                try {
                    if (typeof showLoading === 'function') {
                        showLoading(false);
                    }
                } catch (e) {
                    console.log('showLoading not available yet');
                }
            });
    }

    // Progress bar containers and elements - initialize at the top level
    let progressBarContainer = null;
    let progressBar = null;
    let progressText = null;
    let parsingErrorsList = null;

    // Search progress bar elements
    let searchProgressContainer = null;
    let searchProgressBar = null;
    let searchProgressText = null;

    // Load progress bar elements
    let loadProgressContainer = null;
    let loadProgressBar = null;
    let loadProgressText = null;

    // Upload progress bar elements
    let uploadProgressContainer = null;
    let uploadProgressBar = null;
    let uploadProgressText = null;

    /**
     * Creates a generic animated progress bar
     * @param {string} id - The ID for the progress bar container
     * @param {string} title - The title to display above the progress bar
     * @param {string} initialText - The initial text to display
     * @param {string} containerClass - Additional CSS class for the container
     * @param {string} barColor - Bootstrap color class for the progress bar (e.g., 'primary', 'success')
     * @param {Element} insertBeforeElement - Element to insert the progress bar before
     * @returns {Object} - Object containing the container, bar, and text elements
     */
    function createGenericProgressBar(id, title, initialText, containerClass, barColor, insertBeforeElement) {
        // Create container
        const container = document.createElement('div');
        container.id = id;
        container.className = `progress-container ${containerClass || ''}`;
        container.style.display = 'none';
        container.style.margin = '15px 0';
        container.style.padding = '15px';
        container.style.borderRadius = '8px';
        container.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';

        // Create title
        const titleElement = document.createElement('h5');
        titleElement.className = 'mb-3';
        titleElement.textContent = title;
        titleElement.style.color = 'var(--text-bright)';

        // Create progress bar wrapper
        const progressBarWrapper = document.createElement('div');
        progressBarWrapper.className = 'progress';
        progressBarWrapper.style.height = '25px';
        progressBarWrapper.style.marginBottom = '15px';
        progressBarWrapper.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
        progressBarWrapper.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.2) inset';

        // Create progress bar
        const bar = document.createElement('div');
        bar.className = `progress-bar progress-bar-striped progress-bar-animated bg-${barColor || 'primary'}`;
        bar.role = 'progressbar';
        bar.style.width = '0%';
        bar.style.color = 'white';
        bar.style.fontWeight = 'bold';
        bar.style.textAlign = 'center';
        bar.style.fontSize = '14px';
        bar.style.lineHeight = '25px';
        bar.style.transition = 'width 0.5s ease-in-out, background-color 0.5s ease';
        bar.textContent = '0%';
        bar.setAttribute('aria-valuenow', '0');
        bar.setAttribute('aria-valuemin', '0');
        bar.setAttribute('aria-valuemax', '100');

        progressBarWrapper.appendChild(bar);

        // Create progress text
        const text = document.createElement('div');
        text.className = 'mt-2 mb-2';
        text.textContent = initialText || 'Initializing...';
        text.style.color = 'var(--text-bright)';
        text.style.fontSize = '14px';

        // Add elements to container
        container.appendChild(titleElement);
        container.appendChild(progressBarWrapper);
        container.appendChild(text);

        // Add to the page
        const parentElement = insertBeforeElement.parentElement;
        parentElement.insertBefore(container, insertBeforeElement);

        return { container, bar, text };
    }

    /**
     * Updates a generic progress bar
     * @param {Object} progressElements - Object containing the bar and text elements
     * @param {number} percent - Percentage of completion (0-100)
     * @param {string} message - Message to display
     * @param {boolean} isComplete - Whether the operation is complete
     */
    function updateGenericProgressBar(progressElements, percent, message, isComplete = false) {
        const { bar, text } = progressElements;

        if (!bar || !text) return;

        // Ensure percent is between 0 and 100
        percent = Math.max(0, Math.min(100, Math.round(percent)));

        // Animate the progress bar with smooth transition
        bar.style.width = `${percent}%`;
        bar.setAttribute('aria-valuenow', percent);
        bar.textContent = `${percent}%`;

        // Update text
        text.innerHTML = message || `${percent}% complete`;

        // If complete, change color and remove animation
        if (isComplete || percent >= 100) {
            bar.classList.remove('bg-primary', 'bg-info');
            bar.classList.add('bg-success');

            // Add a completion effect
            bar.style.boxShadow = '0 0 10px rgba(40, 167, 69, 0.5)';

            // Optional: pulse effect on completion
            if (!bar.classList.contains('completion-pulse')) {
                bar.classList.add('completion-pulse');

                // Remove the pulse effect after a few seconds
                setTimeout(() => {
                    bar.classList.remove('completion-pulse');
                }, 3000);
            }
        } else if (percent < 30) {
            bar.classList.remove('bg-success');
            bar.classList.add('bg-info');
        } else {
            bar.classList.remove('bg-success', 'bg-info');
            bar.classList.add('bg-primary');
        }

        // Keep animation for incomplete progress
        if (!isComplete && percent < 100) {
            bar.classList.add('progress-bar-animated');
        } else {
            bar.classList.remove('progress-bar-animated');
        }
    }

    /**
     * Shows or hides a generic progress bar
     * @param {Object} progressElements - Object containing the container element
     * @param {boolean} show - Whether to show or hide the progress bar
     */
    function toggleGenericProgressBar(progressElements, show) {
        try {
            const { container } = progressElements;
            if (container) {
                container.style.display = show ? 'block' : 'none';
            } else {
                console.warn('Progress bar container not available');
            }
        } catch (error) {
            console.error('Error toggling progress bar visibility:', error);
            // Continue without toggling the progress bar
        }
    }

    /**
     * Creates the parsing progress bar
     */
    function createProgressBar() {
        // Create progress bar container if it doesn't exist
        if (!progressBarContainer) {
            progressBarContainer = document.createElement('div');
            progressBarContainer.className = 'parsing-progress-container';
            progressBarContainer.style.display = 'none';
            progressBarContainer.style.margin = '15px 0';
            progressBarContainer.style.padding = '15px';
            progressBarContainer.style.borderRadius = '8px';
            progressBarContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';

            // Create title
            const title = document.createElement('h5');
            title.className = 'mb-3';
            title.textContent = 'Parsing Progress';
            title.style.color = 'var(--text-bright)';

            // Create progress bar
            const progressBarWrapper = document.createElement('div');
            progressBarWrapper.className = 'progress';
            progressBarWrapper.style.height = '30px';
            progressBarWrapper.style.marginBottom = '15px';
            progressBarWrapper.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
            progressBarWrapper.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.2) inset';

            progressBar = document.createElement('div');
            progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-primary';
            progressBar.role = 'progressbar';
            progressBar.style.width = '0%';
            progressBar.style.color = 'white';
            progressBar.style.fontWeight = 'bold';
            progressBar.style.textAlign = 'center';
            progressBar.style.fontSize = '16px';
            progressBar.style.lineHeight = '30px';
            progressBar.style.transition = 'width 0.5s ease-in-out, background-color 0.5s ease';
            progressBar.textContent = '0%';
            progressBar.setAttribute('aria-valuenow', '0');
            progressBar.setAttribute('aria-valuemin', '0');
            progressBar.setAttribute('aria-valuemax', '100');

            progressBarWrapper.appendChild(progressBar);

            // Create progress text
            progressText = document.createElement('div');
            progressText.className = 'mt-2 mb-3';
            progressText.textContent = 'Initializing...';
            progressText.style.color = 'var(--text-bright)';
            progressText.style.fontSize = '14px';

            // Create errors container
            const errorsContainer = document.createElement('div');
            errorsContainer.className = 'mt-4';

            const errorsTitle = document.createElement('h6');
            errorsTitle.textContent = 'Parsing Errors:';
            errorsTitle.className = 'text-danger mb-2';

            parsingErrorsList = document.createElement('ul');
            parsingErrorsList.className = 'list-group';

            errorsContainer.appendChild(errorsTitle);
            errorsContainer.appendChild(parsingErrorsList);

            // Add all elements to the container
            progressBarContainer.appendChild(title);
            progressBarContainer.appendChild(progressBarWrapper);
            progressBarContainer.appendChild(progressText);
            progressBarContainer.appendChild(errorsContainer);

            // Add to the page
            const container = document.querySelector('.container-fluid');
            if (eventsTableContainer) {
                container.insertBefore(progressBarContainer, eventsTableContainer);
            } else {
                // If eventsTableContainer is not available, just append to the container
                container.appendChild(progressBarContainer);
                console.log('Using fallback container for progress bar');
            }
        }

        // Reset progress bar
        progressBar.style.width = '0%';
        progressBar.setAttribute('aria-valuenow', '0');
        progressText.textContent = 'Initializing...';
        parsingErrorsList.innerHTML = '';

        // Show progress bar
        progressBarContainer.style.display = 'block';
    }

    /**
     * Creates the search progress bar
     */
    function createSearchProgressBar() {
        if (!searchProgressContainer) {
            // Find a suitable container for the progress bar
            let targetContainer = eventsTableContainer;
            if (!targetContainer) {
                // Fallback to other containers if eventsTableContainer is not available
                targetContainer = document.querySelector('.container-fluid') || document.body;
                console.log('Using fallback container for search progress bar');
            }

            const elements = createGenericProgressBar(
                'search-progress-container',
                'Search Progress',
                'Preparing search...',
                'search-progress',
                'info',
                targetContainer
            );

            searchProgressContainer = elements.container;
            searchProgressBar = elements.bar;
            searchProgressText = elements.text;
        }

        // Reset and show the progress bar
        if (searchProgressBar && searchProgressText) {
            updateGenericProgressBar({ bar: searchProgressBar, text: searchProgressText }, 0, 'Preparing search...');
            toggleGenericProgressBar({ container: searchProgressContainer }, true);
        } else {
            console.error('Search progress bar elements not properly initialized');
        }
    }

    /**
     * Creates the load progress bar
     */
    function createLoadProgressBar() {
        if (!loadProgressContainer) {
            // Find a suitable container for the progress bar
            let targetContainer = eventsTableContainer;
            if (!targetContainer) {
                // Fallback to other containers if eventsTableContainer is not available
                targetContainer = document.querySelector('.container-fluid') || document.body;
                console.log('Using fallback container for load progress bar');
            }

            const elements = createGenericProgressBar(
                'load-progress-container',
                'Loading Events',
                'Preparing to load events...',
                'load-progress',
                'primary',
                targetContainer
            );

            loadProgressContainer = elements.container;
            loadProgressBar = elements.bar;
            loadProgressText = elements.text;
        }

        // Reset and show the progress bar
        if (loadProgressBar && loadProgressText) {
            updateGenericProgressBar({ bar: loadProgressBar, text: loadProgressText }, 0, 'Preparing to load events...');
            toggleGenericProgressBar({ container: loadProgressContainer }, true);
        } else {
            console.error('Load progress bar elements not properly initialized');
        }
    }

    function updateProgressBar(processed, total, errors = []) {
        if (!progressBar || !progressText) return;

        const percent = Math.round((processed / total) * 100);
        const isComplete = processed >= total;

        // Animate the progress bar
        progressBar.style.width = `${percent}%`;
        progressBar.setAttribute('aria-valuenow', percent);
        progressBar.textContent = `${percent}%`;

        // Add more detailed progress information
        progressText.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span>Processing file ${processed} of ${total}</span>
                <span class="badge bg-info">${percent}% Complete</span>
            </div>
            <div class="small text-muted mt-1">
                ${errors.length > 0 ? `${errors.length} errors found` : 'No errors detected'}
            </div>
        `;

        // Update errors list
        if (errors.length > 0) {
            parsingErrorsList.innerHTML = '';
            errors.forEach(error => {
                const li = document.createElement('li');
                li.className = 'list-group-item list-group-item-danger';
                li.innerHTML = `<strong>${error.file}:</strong> ${error.error}`;
                parsingErrorsList.appendChild(li);
            });
        }

        // Make the progress bar pulse when it's not at 100%
        if (percent < 100) {
            progressBar.classList.add('progress-bar-animated');
        } else {
            progressBar.classList.remove('progress-bar-animated');
            // Change color to success when complete
            progressBar.classList.remove('bg-primary');
            progressBar.classList.add('bg-success');

            // Add a completion effect
            progressBar.style.boxShadow = '0 0 10px rgba(40, 167, 69, 0.5)';
        }
    }

    /**
     * Updates the search progress bar
     * @param {number} percent - Percentage of completion (0-100)
     * @param {string} message - Message to display
     * @param {boolean} isComplete - Whether the search is complete
     */
    function updateSearchProgress(percent, message, isComplete = false) {
        try {
            if (!searchProgressBar || !searchProgressText) {
                console.warn('Search progress bar elements not available');
                return;
            }

            updateGenericProgressBar(
                { bar: searchProgressBar, text: searchProgressText },
                percent,
                message,
                isComplete
            );

            // Hide the progress bar after completion with a delay
            if (isComplete && searchProgressContainer) {
                setTimeout(() => {
                    toggleGenericProgressBar({ container: searchProgressContainer }, false);
                }, 3000);
            }
        } catch (error) {
            console.error('Error updating search progress:', error);
            // Continue without updating the progress bar
        }
    }

    /**
     * Updates the load progress bar
     * @param {number} percent - Percentage of completion (0-100)
     * @param {string} message - Message to display
     * @param {boolean} isComplete - Whether the loading is complete
     */
    function updateLoadProgress(percent, message, isComplete = false) {
        try {
            if (!loadProgressBar || !loadProgressText) {
                console.warn('Load progress bar elements not available');
                return;
            }

            updateGenericProgressBar(
                { bar: loadProgressBar, text: loadProgressText },
                percent,
                message,
                isComplete
            );

            // Hide the progress bar after completion with a delay
            if (isComplete && loadProgressContainer) {
                setTimeout(() => {
                    toggleGenericProgressBar({ container: loadProgressContainer }, false);
                }, 3000);
            }
        } catch (error) {
            console.error('Error updating load progress:', error);
            // Continue without updating the progress bar
        }
    }

    function hideProgressBar() {
        if (progressBarContainer) {
            progressBarContainer.style.display = 'none';
        }
    }

    function parseLocalFolder(folderPath, recursive) {
        showLoading(true);
        createProgressBar();

        // Set initial progress
        updateProgressBar(0, 1, []);

        let pollingStarted = false;
        let statusPollInterval;

        // Function to start polling
        function startPolling() {
            if (pollingStarted) return;
            pollingStarted = true;

            // Start polling for status updates
            statusPollInterval = setInterval(() => {
                fetch('/api/parsing-status')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.status && data.parsingStatus) {
                            const status = data.parsingStatus;

                            if (status.inProgress) {
                                // Update progress bar
                                updateProgressBar(status.processedFiles, status.totalFiles, status.errors);
                            } else if (status.totalFiles > 0) {
                                // Parsing completed, stop polling
                                clearInterval(statusPollInterval);

                                // If parsing is complete, reload events and show success message
                                if (status.processedFiles >= status.totalFiles) {
                                    setTimeout(() => {
                                        // Show final status
                                        updateProgressBar(status.totalFiles, status.totalFiles, status.errors);

                                        // Create a summary message
                                        const summary = document.createElement('div');
                                        summary.className = 'alert alert-success mt-3';
                                        summary.innerHTML = `
                                            <strong>Parsing completed:</strong>
                                            Successfully processed ${status.successfulFiles || status.processedFiles} files with ${status.failedFiles || 0} failures.
                                            Imported ${status.totalEvents} events.
                                            <br>
                                            <small>Storage type: ${status.storageType || 'unknown'}</small>
                                        `;

                                        // Insert before the errors list
                                        progressBarContainer.insertBefore(summary, parsingErrorsList.parentNode);

                                        // Hide loading spinner
                                        showLoading(false);

                                        // Reload events
                                        loadEvents();

                                        // Switch to table view
                                        showView('table');
                                    }, 500);
                                }
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error polling parsing status:', error);
                    });
            }, 250); // Poll every 250ms for smoother updates
        }

        // Start the parsing process
        fetch('/api/parse-folder', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                folderPath,
                recursive
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status) {
                // Start polling for status updates
                startPolling();

                // Update initial progress
                if (data.totalFiles) {
                    updateProgressBar(0, data.totalFiles, []);
                }

                console.log('Started parsing folder:', data.message);
            } else {
                hideProgressBar();
                showLoading(false);
                alert(`Error: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Error parsing folder:', error);
            if (statusPollInterval) {
                clearInterval(statusPollInterval);
            }
            hideProgressBar();
            showLoading(false);
            alert('Error parsing folder. See console for details.');
        });
    }

    function updateDetailFields(events) {
        // Extract all detail fields from events
        const fields = new Set();

        events.forEach(event => {
            if (event.details && typeof event.details === 'object') {
                extractDetailFields(event.details, '', fields);
            }
        });

        // Convert to array and sort
        detailFields = Array.from(fields).sort();

        // Update the select element
        populateDetailFieldsSelect();
    }

    function extractDetailFields(obj, prefix, fields) {
        if (!obj || typeof obj !== 'object') return;

        // Limit recursion depth to prevent performance issues with deeply nested objects
        const maxDepth = 3;
        const currentDepth = prefix.split('.').filter(p => p).length;

        if (currentDepth >= maxDepth) return;

        for (const key in obj) {
            const fullPath = prefix ? `${prefix}.${key}` : key;

            // Skip very long arrays to improve performance
            if (Array.isArray(obj[key]) && obj[key].length > 20) {
                fields.add(fullPath);
                continue;
            }

            if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
                // Recurse into nested objects
                extractDetailFields(obj[key], fullPath, fields);

                // Also add the object itself as a field
                fields.add(fullPath);
            } else {
                // Add leaf fields
                fields.add(fullPath);
            }
        }
    }

    function populateDetailFieldsSelect() {
        // Clear existing options except the first one
        while (detailFieldsSelect.options.length > 1) {
            detailFieldsSelect.remove(1);
        }

        // Add options for each detail field
        detailFields.forEach(field => {
            // Skip fields that are already added as columns
            if (!availableColumns.some(col => col.id === `details.${field}`)) {
                const option = document.createElement('option');
                option.value = field;
                option.textContent = field;
                detailFieldsSelect.appendChild(option);
            }
        });
    }

    function populateColumnSelectionModal() {
        console.log('populateColumnSelectionModal called');
        console.log('columnCheckboxes element:', columnCheckboxes);
        console.log('availableColumns:', availableColumns);

        // Clear existing checkboxes
        columnCheckboxes.innerHTML = '';

        // Ensure we have at least the basic columns
        if (availableColumns.length === 0) {
            availableColumns = [
                { id: 'timestamp', label: 'Timestamp', visible: true },
                { id: 'event_type', label: 'Event Type', visible: true },
                { id: 'source', label: 'Source', visible: true },
                { id: 'file_name', label: 'File Name', visible: true },
                { id: 'artifact_name', label: 'Artifact Name', visible: true, constant: true },
                { id: 'description', label: 'Description', visible: true },
                { id: 'upload_date', label: 'Upload Date', visible: false }
            ];
        }

        // Group columns by type
        const baseColumns = [];
        const detailColumns = [];
        const otherColumns = [];

        availableColumns.forEach(column => {
            if (['timestamp', 'event_type', 'source', 'file_name', 'artifact_name', 'description', 'upload_date'].includes(column.id)) {
                baseColumns.push(column);
            } else if (column.id.startsWith('details.')) {
                detailColumns.push(column);
            } else {
                otherColumns.push(column);
            }
        });

        console.log('baseColumns:', baseColumns);
        console.log('detailColumns:', detailColumns);
        console.log('otherColumns:', otherColumns);

        // Add a simple test message to the modal
        const testDiv = document.createElement('div');
        testDiv.className = 'alert alert-info';
        testDiv.innerHTML = `<strong>Column Selection Working!</strong><br>
                            Available columns: ${availableColumns.length}<br>
                            Base columns: ${baseColumns.length}<br>
                            Detail columns: ${detailColumns.length}`;
        columnCheckboxes.appendChild(testDiv);

        // Column detection is now done before calling this function, so no need for early return

        // Add buttons for quick selection
        const quickSelectDiv = document.createElement('div');
        quickSelectDiv.className = 'mb-3 d-flex gap-2';

        const selectAllBtn = document.createElement('button');
        selectAllBtn.className = 'btn btn-sm btn-outline-primary';
        selectAllBtn.textContent = 'Select All';
        selectAllBtn.type = 'button';
        selectAllBtn.addEventListener('click', function() {
            const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                // Skip disabled checkboxes (constant columns)
                if (!checkbox.disabled) {
                    checkbox.checked = true;
                }
            });
            selectAllColumns.checked = true;
        });

        const deselectAllBtn = document.createElement('button');
        deselectAllBtn.className = 'btn btn-sm btn-outline-secondary';
        deselectAllBtn.textContent = 'Deselect All';
        deselectAllBtn.type = 'button';
        deselectAllBtn.addEventListener('click', function() {
            const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                // Skip disabled checkboxes (constant columns)
                if (!checkbox.disabled) {
                    checkbox.checked = false;
                }
            });
            selectAllColumns.checked = false;
        });

        const resetBtn = document.createElement('button');
        resetBtn.className = 'btn btn-sm btn-outline-danger';
        resetBtn.textContent = 'Reset to Default';
        resetBtn.type = 'button';
        resetBtn.addEventListener('click', function() {
            const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                const columnId = checkbox.dataset.columnId;
                // Default columns are timestamp, event_type, source, file_name, artifact_name, description
                // Skip constant columns as they're always checked and disabled
                if (!checkbox.disabled) {
                    checkbox.checked = ['timestamp', 'event_type', 'source', 'file_name', 'artifact_name', 'description'].includes(columnId);
                }
            });
            selectAllColumns.checked = false;
        });

        quickSelectDiv.appendChild(selectAllBtn);
        quickSelectDiv.appendChild(deselectAllBtn);
        quickSelectDiv.appendChild(resetBtn);

        columnCheckboxes.appendChild(quickSelectDiv);

        // Add section for base columns
        if (baseColumns.length > 0) {
            const sectionHeader = document.createElement('h6');
            sectionHeader.className = 'mt-3 mb-2 text-primary';
            sectionHeader.textContent = 'Basic Columns';
            columnCheckboxes.appendChild(sectionHeader);

            addColumnCheckboxes(baseColumns);
        }

        // Add section for other columns
        if (otherColumns.length > 0) {
            const sectionHeader = document.createElement('h6');
            sectionHeader.className = 'mt-4 mb-2 text-primary';
            sectionHeader.textContent = 'Additional Columns';
            columnCheckboxes.appendChild(sectionHeader);

            addColumnCheckboxes(otherColumns);
        }

        // Add section for detail columns
        if (detailColumns.length > 0) {
            const sectionHeader = document.createElement('h6');
            sectionHeader.className = 'mt-4 mb-2 text-primary';
            sectionHeader.textContent = 'Detail Fields';
            columnCheckboxes.appendChild(sectionHeader);

            addColumnCheckboxes(detailColumns);
        }

        // Add section for adding new detail fields
        const addDetailFieldsSection = document.createElement('div');
        addDetailFieldsSection.className = 'mt-4 mb-3';

        const addDetailHeader = document.createElement('h6');
        addDetailHeader.className = 'mb-3 text-primary';
        addDetailHeader.textContent = 'Add Detail Fields';
        addDetailFieldsSection.appendChild(addDetailHeader);

        // Create a searchable dropdown for detail fields
        const detailFieldContainer = document.createElement('div');
        detailFieldContainer.className = 'mb-3';

        const detailFieldLabel = document.createElement('label');
        detailFieldLabel.className = 'form-label text-white';
        detailFieldLabel.textContent = 'Search and add detail fields:';
        detailFieldContainer.appendChild(detailFieldLabel);

        const detailFieldInput = document.createElement('input');
        detailFieldInput.type = 'text';
        detailFieldInput.className = 'form-control';
        detailFieldInput.placeholder = 'Type to search detail fields (e.g., System.EventID, UserData.Data)';
        detailFieldInput.id = 'detail-field-search';
        detailFieldContainer.appendChild(detailFieldInput);

        // Create dropdown for suggestions
        const suggestionsDropdown = document.createElement('div');
        suggestionsDropdown.className = 'dropdown-menu w-100';
        suggestionsDropdown.id = 'detail-field-suggestions';
        suggestionsDropdown.style.maxHeight = '200px';
        suggestionsDropdown.style.overflowY = 'auto';
        detailFieldContainer.appendChild(suggestionsDropdown);

        addDetailFieldsSection.appendChild(detailFieldContainer);

        // Add button to add custom field
        const addCustomFieldBtn = document.createElement('button');
        addCustomFieldBtn.type = 'button';
        addCustomFieldBtn.className = 'btn btn-sm btn-success me-2';
        addCustomFieldBtn.textContent = 'Add Field';
        addCustomFieldBtn.addEventListener('click', function() {
            const fieldPath = detailFieldInput.value.trim();
            if (fieldPath) {
                addDetailFieldAsColumn(fieldPath);
                detailFieldInput.value = '';
                suggestionsDropdown.classList.remove('show');
                // Just update the checkboxes without repopulating the entire modal
                updateColumnCheckboxes();
            }
        });
        addDetailFieldsSection.appendChild(addCustomFieldBtn);

        // Add help text
        const helpText = document.createElement('small');
        helpText.className = 'text-muted';
        helpText.innerHTML = 'Enter field paths like <code>System.EventID</code> or <code>UserData.Data.Name</code> to add them as columns.';
        addDetailFieldsSection.appendChild(helpText);

        columnCheckboxes.appendChild(addDetailFieldsSection);

        // Populate detail field suggestions
        populateDetailFieldSuggestions();

        // Update select all checkbox
        selectAllColumns.checked = availableColumns.every(col => col.visible);

        // Clear selected detail fields
        selectedDetailFields.innerHTML = '';

        // Add badges for detail fields that are columns
        availableColumns.forEach(column => {
            if (column.id.startsWith('details.')) {
                addDetailFieldBadge(column.id.substring(8), column.label);
            }
        });

        // Update detail fields select
        populateDetailFieldsSelect();
    }

    function addColumnCheckboxes(columns) {
        columns.forEach(column => {
            const div = document.createElement('div');
            div.className = 'form-check mb-2';

            const input = document.createElement('input');
            input.className = 'form-check-input';
            input.type = 'checkbox';
            input.id = `column-${column.id}`;
            input.checked = column.visible;
            input.dataset.columnId = column.id;

            // If this is a constant column, disable the checkbox and add a note
            if (column.constant) {
                input.disabled = true;
                input.checked = true; // Always checked
                div.className += ' opacity-75'; // Make it slightly faded
            }

            const label = document.createElement('label');
            label.className = 'form-check-label';
            label.htmlFor = `column-${column.id}`;
            label.textContent = column.label;

            // Add a badge for constant columns
            if (column.constant) {
                const badge = document.createElement('span');
                badge.className = 'badge bg-info ms-2';
                badge.textContent = 'Required';
                badge.style.fontSize = '0.7rem';
                label.appendChild(badge);
            }

            div.appendChild(input);
            div.appendChild(label);
            columnCheckboxes.appendChild(div);
        });
    }

    function addDetailField(field) {
        // Check if this field is already a column
        if (availableColumns.some(col => col.id === `details.${field}`)) {
            return;
        }

        // Add to available columns
        const label = field.split('.').pop();
        availableColumns.push({
            id: `details.${field}`,
            label: label.charAt(0).toUpperCase() + label.slice(1),
            visible: true
        });

        // Add badge to selected detail fields
        addDetailFieldBadge(field);

        // Update detail fields select
        populateDetailFieldsSelect();
    }

    function addDetailFieldAsColumn(fieldPath) {
        const columnId = `details.${fieldPath}`;

        // Check if this field is already a column
        const existingColumn = availableColumns.find(col => col.id === columnId);
        if (existingColumn) {
            console.log(`Field ${fieldPath} already exists as a column, making it visible`);
            existingColumn.visible = true;
            return;
        }

        // Create a proper label from the field path
        const label = fieldPath.split('.').pop().replace(/_/g, ' ');
        const formattedLabel = label.charAt(0).toUpperCase() + label.slice(1);

        // Add to available columns with visible set to true
        const newColumn = {
            id: columnId,
            label: formattedLabel,
            visible: true
        };

        availableColumns.push(newColumn);

        console.log(`Added detail field as column: ${fieldPath}`, newColumn);
        console.log('Updated availableColumns:', availableColumns);
    }

    function updateColumnCheckboxes() {
        // Find the detail fields section header
        const headers = columnCheckboxes.querySelectorAll('h6');
        let detailFieldsSection = null;

        headers.forEach(header => {
            if (header.textContent.includes('Detail Fields')) {
                detailFieldsSection = header;
            }
        });

        if (detailFieldsSection) {
            // Get all detail columns
            const detailColumns = availableColumns.filter(col => col.id.startsWith('details.'));

            // Find the last detail field checkbox
            const existingDetailCheckboxes = columnCheckboxes.querySelectorAll('input[data-column-id^="details."]');
            let insertAfter = detailFieldsSection;

            if (existingDetailCheckboxes.length > 0) {
                insertAfter = existingDetailCheckboxes[existingDetailCheckboxes.length - 1].closest('.form-check');
            }

            // Add any new detail columns that don't have checkboxes yet
            detailColumns.forEach(column => {
                const existingCheckbox = columnCheckboxes.querySelector(`input[data-column-id="${column.id}"]`);
                if (!existingCheckbox) {
                    const div = document.createElement('div');
                    div.className = 'form-check mb-2';

                    const input = document.createElement('input');
                    input.className = 'form-check-input';
                    input.type = 'checkbox';
                    input.id = `column-${column.id}`;
                    input.checked = column.visible;
                    input.dataset.columnId = column.id;

                    const label = document.createElement('label');
                    label.className = 'form-check-label';
                    label.htmlFor = `column-${column.id}`;
                    label.textContent = column.label;

                    div.appendChild(input);
                    div.appendChild(label);

                    // Insert after the last detail field or the section header
                    if (insertAfter.nextSibling) {
                        insertAfter.parentNode.insertBefore(div, insertAfter.nextSibling);
                    } else {
                        insertAfter.parentNode.appendChild(div);
                    }
                    insertAfter = div;

                    console.log(`Added checkbox for new detail field: ${column.label}`);
                }
            });
        } else {
            console.log('Detail Fields section not found, will be added on next modal open');
        }
    }

    function populateDetailFieldSuggestions() {
        const suggestionsDropdown = document.getElementById('detail-field-suggestions');
        const detailFieldInput = document.getElementById('detail-field-search');

        if (!suggestionsDropdown || !detailFieldInput) return;

        // Collect all available detail fields from current events
        const allDetailFields = new Set();

        if (currentEvents && currentEvents.length > 0) {
            currentEvents.slice(0, 50).forEach(event => {
                let detailsObj = event.details;

                // Parse details if it's a string
                if (typeof event.details === 'string') {
                    try {
                        detailsObj = JSON.parse(event.details);
                    } catch (e) {
                        detailsObj = null;
                    }
                }

                if (detailsObj && typeof detailsObj === 'object') {
                    extractDetailFields(detailsObj, '', allDetailFields);
                }
            });
        }

        // Add input event listener for search
        detailFieldInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            suggestionsDropdown.innerHTML = '';

            if (searchTerm.length < 2) {
                suggestionsDropdown.classList.remove('show');
                return;
            }

            // Filter fields based on search term
            const matchingFields = Array.from(allDetailFields)
                .filter(field => field.toLowerCase().includes(searchTerm))
                .slice(0, 10); // Limit to 10 suggestions

            if (matchingFields.length > 0) {
                matchingFields.forEach(field => {
                    const suggestionItem = document.createElement('a');
                    suggestionItem.className = 'dropdown-item';
                    suggestionItem.href = '#';
                    suggestionItem.textContent = field;

                    suggestionItem.addEventListener('click', function(e) {
                        e.preventDefault();
                        detailFieldInput.value = field;
                        suggestionsDropdown.classList.remove('show');
                    });

                    suggestionsDropdown.appendChild(suggestionItem);
                });

                suggestionsDropdown.classList.add('show');
            } else {
                suggestionsDropdown.classList.remove('show');
            }
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!detailFieldInput.contains(e.target) && !suggestionsDropdown.contains(e.target)) {
                suggestionsDropdown.classList.remove('show');
            }
        });
    }

    function addDetailFieldBadge(field, label) {
        const badge = document.createElement('span');
        badge.className = 'badge bg-primary me-2 mb-2';
        badge.textContent = label || field;
        badge.dataset.field = field;

        const removeBtn = document.createElement('button');
        removeBtn.className = 'btn-close btn-close-white ms-1';
        removeBtn.style.fontSize = '0.5rem';
        removeBtn.setAttribute('aria-label', 'Remove');

        removeBtn.addEventListener('click', function() {
            // Remove from available columns
            const index = availableColumns.findIndex(col => col.id === `details.${field}`);
            if (index !== -1) {
                availableColumns.splice(index, 1);
            }

            // Remove badge
            badge.remove();

            // Update detail fields select
            populateDetailFieldsSelect();
        });

        badge.appendChild(removeBtn);
        selectedDetailFields.appendChild(badge);
    }

    function applyColumnSelection() {
        console.log('applyColumnSelection called');

        // Check if modal is still open and force close it
        const modalElement = document.getElementById('column-select-modal');
        if (modalElement && modalElement.classList.contains('show')) {
            console.warn('Modal still open during applyColumnSelection, forcing close');
            forceCloseModal();
        }

        // Always update column visibility based on checkboxes
        const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]');
        console.log('Found checkboxes:', checkboxes.length);

        // First, set all non-constant columns to invisible
        availableColumns.forEach(column => {
            // Skip constant columns - they should always remain visible
            if (!column.constant) {
                column.visible = false;
            }
        });

        // Then set only the checked columns to visible
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const columnId = checkbox.dataset.columnId;
                const column = availableColumns.find(col => col.id === columnId);
                console.log(`Setting column ${columnId} to visible:`, column);
                if (column) {
                    column.visible = true;
                }
            }
        });

        console.log('Final availableColumns after selection:', availableColumns);
        console.log('Visible columns:', availableColumns.filter(col => col.visible));

        // Update table headers
        updateTableHeaders();

        // Clear the table body
        eventsTableBody.innerHTML = '';

        // Re-render the table with the current events
        if (currentEvents.length > 0) {
            console.log('Re-rendering events with new column selection');
            console.log('Available columns for rendering:', availableColumns);
            console.log('Visible columns for rendering:', availableColumns.filter(col => col.visible));

            // Force a complete re-render
            renderEvents(currentEvents, false); // Don't auto-detect columns again

            // Update showing info to reflect the new column selection
            updateShowingInfo();
        }

        // Save column configuration to localStorage
        saveColumnConfiguration();

        // Show a confirmation message (but make sure modal is still closed)
        setTimeout(() => {
            // Double-check modal is closed before showing success message
            const modalStillOpen = modalElement && modalElement.classList.contains('show');
            if (modalStillOpen) {
                console.warn('Modal reopened, forcing close again');
                forceCloseModal();
            }

            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.setAttribute('role', 'alert');
            alertDiv.innerHTML = `
                <strong>Success!</strong> Column selection has been updated. Visible columns: ${availableColumns.filter(col => col.visible).length}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            // Add the alert to the page
            const container = document.querySelector('.container-fluid');
            container.insertBefore(alertDiv, container.firstChild);

            // Auto-dismiss after 3 seconds
            setTimeout(() => {
                try {
                    const bsAlert = new bootstrap.Alert(alertDiv);
                    bsAlert.close();
                } catch (e) {
                    // Fallback if Bootstrap alert fails
                    alertDiv.remove();
                }
            }, 3000);
        }, 200);
    }

    function saveColumnConfiguration() {
        // Save column visibility settings to localStorage
        const columnConfig = availableColumns.map(col => ({
            id: col.id,
            visible: col.visible
        }));

        try {
            localStorage.setItem('columnConfig', JSON.stringify(columnConfig));
        } catch (e) {
            console.error('Failed to save column configuration:', e);
        }
    }

    function loadColumnConfiguration() {
        try {
            const savedConfig = localStorage.getItem('columnConfig');
            if (savedConfig) {
                const columnConfig = JSON.parse(savedConfig);

                // Apply saved visibility settings to available columns
                columnConfig.forEach(savedCol => {
                    const column = availableColumns.find(col => col.id === savedCol.id);
                    if (column) {
                        column.visible = savedCol.visible;
                    }
                });
            }
        } catch (e) {
            console.error('Failed to load column configuration:', e);
        }
    }

    // Load column configuration now that the function is defined
    loadColumnConfiguration();

    function loadFiles() {
        showLoading(true);

        fetch('/api/files')
            .then(response => response.json())
            .then(data => {
                if (data.status) {
                    renderFiles(data.files);
                } else {
                    console.error('Error loading files:', data.message);
                    alert(`Error loading files: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error loading files:', error);
                alert('Error loading files. See console for details.');
            })
            .finally(() => {
                showLoading(false);
            });
    }

    function renderFiles(files) {
        // Clear existing data
        filesTableBody.innerHTML = '';

        if (files.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="4" class="text-center">No files uploaded yet</td>
            `;
            filesTableBody.appendChild(row);
            return;
        }

        // Sort files by modified date (newest first)
        files.sort((a, b) => new Date(b.modified) - new Date(a.modified));

        // Populate table
        files.forEach(file => {
            const row = document.createElement('tr');

            const fileSize = formatFileSize(file.size);
            const modified = new Date(file.modified).toLocaleString();

            row.innerHTML = `
                <td>${file.name}</td>
                <td>${fileSize}</td>
                <td>${modified}</td>
                <td>
                    <button class="btn btn-sm btn-primary view-file-btn" data-filename="${file.name}" title="View file content">
                        <i class="bi bi-eye"></i> View
                    </button>
                    <button class="btn btn-sm btn-success parse-file-btn" data-filename="${file.name}" title="Parse file and add events">
                        <i class="bi bi-play-circle"></i> Parse
                    </button>
                    <button class="btn btn-sm btn-danger delete-file-btn" data-filename="${file.name}" title="Delete file and associated events">
                        <i class="bi bi-trash"></i> Delete
                    </button>
                </td>
            `;

            filesTableBody.appendChild(row);

            // Add event listeners for buttons
            row.querySelector('.view-file-btn').addEventListener('click', function() {
                const filename = this.getAttribute('data-filename');
                viewFileContent(filename);
            });

            row.querySelector('.parse-file-btn').addEventListener('click', function() {
                const filename = this.getAttribute('data-filename');
                parseFile(filename);
            });

            row.querySelector('.delete-file-btn').addEventListener('click', function() {
                const filename = this.getAttribute('data-filename');
                deleteFile(filename);
            });
        });
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function viewFileContent(filename) {
        showLoading(true);

        fetch(`/api/raw-file/${filename}`)
            .then(response => response.json())
            .then(data => {
                if (data.status) {
                    // Set modal title
                    fileContentTitle.textContent = `File: ${filename}`;

                    // Set text content
                    fileContentText.textContent = data.content;

                    // Try to parse as JSON for the JSON view
                    try {
                        let jsonData;

                        // Try to parse as a JSON array first
                        try {
                            jsonData = JSON.parse(data.content);
                        } catch (e) {
                            // If that fails, try to parse as JSONL (one JSON object per line)
                            const lines = data.content.split('\\n').filter(line => line.trim());
                            jsonData = [];

                            for (const line of lines) {
                                try {
                                    if (line.trim()) {
                                        jsonData.push(JSON.parse(line));
                                    }
                                } catch (lineError) {
                                    // Skip lines that can't be parsed
                                }
                            }

                            if (jsonData.length === 0) {
                                throw new Error('Could not parse as JSON');
                            }
                        }

                        // Format JSON for display
                        fileContentJson.innerHTML = formatJSON(jsonData);

                        // Enable JSON view button
                        viewAsJsonBtn.disabled = false;

                        // Create table view if possible
                        if (Array.isArray(jsonData) && jsonData.length > 0) {
                            createTableFromJson(jsonData);
                            viewAsTableBtn.disabled = false;
                        } else {
                            viewAsTableBtn.disabled = true;
                        }
                    } catch (e) {
                        // If JSON parsing fails, disable those views
                        fileContentJson.textContent = 'Cannot parse as JSON';
                        viewAsJsonBtn.disabled = true;
                        viewAsTableBtn.disabled = true;
                    }

                    // Show text view by default
                    viewAsTextBtn.click();

                    // Show the modal
                    fileContentModal.show();
                } else {
                    alert(`Error: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error viewing file:', error);
                alert('Error viewing file. See console for details.');
            })
            .finally(() => {
                showLoading(false);
            });
    }

    function createTableFromJson(jsonData) {
        // Clear existing data
        fileContentTableHead.innerHTML = '';
        fileContentTableBody.innerHTML = '';

        // Get all unique keys from the JSON data
        const keys = new Set();
        jsonData.forEach(item => {
            Object.keys(item).forEach(key => keys.add(key));
        });

        // Create table header
        const headerRow = document.createElement('tr');
        keys.forEach(key => {
            const th = document.createElement('th');
            th.textContent = key;
            headerRow.appendChild(th);
        });
        fileContentTableHead.appendChild(headerRow);

        // Create table rows
        jsonData.slice(0, 100).forEach(item => { // Limit to 100 rows for performance
            const row = document.createElement('tr');

            keys.forEach(key => {
                const td = document.createElement('td');

                if (item[key] === undefined || item[key] === null) {
                    td.textContent = '';
                } else if (typeof item[key] === 'object') {
                    td.textContent = JSON.stringify(item[key]);
                } else {
                    td.textContent = item[key];
                }

                row.appendChild(td);
            });

            fileContentTableBody.appendChild(row);
        });

        // Add a message if we limited the rows
        if (jsonData.length > 100) {
            const row = document.createElement('tr');
            const td = document.createElement('td');
            td.colSpan = keys.size;
            td.className = 'text-center font-italic';
            td.textContent = `Showing 100 of ${jsonData.length} rows`;
            row.appendChild(td);
            fileContentTableBody.appendChild(row);
        }
    }

    function parseFile(filename) {
        showLoading(true);

        // Immediately switch to table view
        showView('table');

        // Create a FormData object
        const formData = new FormData();
        formData.append('file', new File([new Blob()], filename, { type: 'application/json' }));

        // Set a custom header to indicate this is a re-parse request
        const headers = {
            'X-Reparse-File': filename
        };

        fetch('/api/upload', {
            method: 'POST',
            body: formData,
            headers: headers
        })
        .then(response => response.json())
        .then(data => {
            if (data.status) {
                alert(`Success: ${data.message}`);

                // Reload events
                loadEvents();

                // Make sure we're showing the table view again after loading events
                setTimeout(function() {
                    showView('table');
                }, 500);
            } else {
                alert(`Error: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Error parsing file:', error);
            alert('Error parsing file. See console for details.');
        })
        .finally(() => {
            showLoading(false);
        });
    }

    function deleteFile(filename) {
        // Show confirmation dialog
        const confirmDelete = confirm(
            `Are you sure you want to delete the file "${filename}"?\n\n` +
            `This will:\n` +
            `• Delete the file from the server\n` +
            `• Remove all associated events from the database\n` +
            `• This action cannot be undone\n\n` +
            `Click OK to proceed or Cancel to abort.`
        );

        if (!confirmDelete) {
            return;
        }

        showLoading(true);

        fetch(`/api/files/${encodeURIComponent(filename)}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.status) {
                const fileSize = (data.fileSize / (1024 * 1024)).toFixed(2);
                alert(
                    `File deleted successfully!\n\n` +
                    `• File: ${data.deletedFile}\n` +
                    `• Size: ${fileSize} MB\n` +
                    `• Events removed: ${data.deletedEvents}`
                );

                // Refresh the files list
                loadFiles();

                // Refresh the events view if we're currently viewing events
                if (currentView === 'table') {
                    loadEvents();
                }

                // Update data status
                updateDataStatus();
            } else {
                alert(`Error deleting file: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Error deleting file:', error);
            alert('Error deleting file. See console for details.');
        })
        .finally(() => {
            showLoading(false);
        });
    }

    // Function to reset and retry loading events
    function resetAndRetry() {
        // Reset counters and flags
        currentRetries = 0;
        isLoading = false;

        // Reset page size to a conservative value
        pageSize = 50;

        // Clear the loading message
        loadingMore.innerHTML = '';
        loadingMore.style.display = 'none';

        // Retry loading events
        loadMoreEvents();
    }

    function eraseAllData() {
        showLoading(true);

        fetch('/api/erase-all-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status) {
                alert(`Success: ${data.message}`);
                // Clear the table
                eventsTableBody.innerHTML = '';
                // Reset event counts
                loadedEvents = 0;
                totalEvents = 0;
                currentEvents = [];
                // Update showing info
                showingInfo.textContent = 'Showing 0 of 0 events';
                // Hide event details
                hideEventDetails();
            } else {
                alert(`Error: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Error erasing data:', error);
            alert('Error erasing data. See console for details.');
        })
        .finally(() => {
            showLoading(false);
        });
    }

    // Functions for handling tagged events
    function toggleEventTag(event, row) {
        if (!event || !event.id) return;

        if (taggedEvents.has(event.id)) {
            // Remove tag
            taggedEvents.delete(event.id);
            if (row) {
                row.classList.remove('tagged-event');
            }
        } else {
            // Add tag
            taggedEvents.set(event.id, event);
            if (row) {
                row.classList.add('tagged-event');
            }
        }

        // Update the tagged count
        updateTaggedCount();

        // Save tagged events to localStorage
        saveTaggedEvents();
    }

    function updateTaggedCount() {
        const taggedCount = document.getElementById('tagged-count');
        taggedCount.textContent = taggedEvents.size;

        // Show/hide the "no tagged events" message
        const noTaggedEvents = document.getElementById('no-tagged-events');
        if (noTaggedEvents) {
            if (taggedEvents.size === 0) {
                noTaggedEvents.style.display = 'block';
            } else {
                noTaggedEvents.style.display = 'none';
            }
        }
    }

    function renderTaggedEvents() {
        const taggedTableBody = document.getElementById('tagged-table-body');
        taggedTableBody.innerHTML = '';

        if (taggedEvents.size === 0) {
            document.getElementById('no-tagged-events').style.display = 'block';
            return;
        }

        document.getElementById('no-tagged-events').style.display = 'none';

        // Convert Map values to array and sort by timestamp
        const sortedEvents = Array.from(taggedEvents.values()).sort((a, b) => {
            return new Date(a.timestamp) - new Date(b.timestamp);
        });

        // Render each tagged event
        sortedEvents.forEach(event => {
            const row = document.createElement('tr');
            row.className = 'event-row tagged-event';
            row.dataset.eventId = event.id;

            // Format timestamp directly with UTC+3 timezone
            let formattedTimestamp = 'N/A';
            const date = new Date(event.timestamp);
            if (!isNaN(date.getTime())) {
                try {
                    // Format the date in UTC+3 timezone
                    const options = {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false,
                        timeZone: 'Europe/Moscow' // UTC+3 timezone
                    };

                    // Format the date with the specified options
                    const formattedDate = date.toLocaleString('en-US', options);

                    // Add milliseconds
                    formattedTimestamp = formattedDate + '.' + String(date.getMilliseconds()).padStart(3, '0') + ' (UTC+3)';
                } catch (error) {
                    // Fallback to standard formatting if timezone formatting fails
                    formattedTimestamp = date.toLocaleString() + '.' + String(date.getMilliseconds()).padStart(3, '0');
                }
            } else {
                formattedTimestamp = event.timestamp || 'N/A';
            }

            // Create cells for each column
            row.innerHTML = `
                <td>${formattedTimestamp}</td>
                <td><span class="event-type-badge event-type-${getEventTypeClass(event.event_type)}">${event.event_type}</span></td>
                <td>${event.source || 'N/A'}</td>
                <td>${event.file_name || 'N/A'}</td>
                <td>${event.description || 'N/A'}</td>
                <td>
                    <button class="btn btn-sm btn-primary view-details-btn">Details</button>
                    <button class="btn btn-sm btn-danger untag-btn"><i class="bi bi-tag-fill"></i> Untag</button>
                </td>
            `;

            taggedTableBody.appendChild(row);

            // Add event listeners
            row.querySelector('.view-details-btn').addEventListener('click', function(e) {
                e.stopPropagation();
                showEventDetailsInPanel(event);
            });

            row.querySelector('.untag-btn').addEventListener('click', function(e) {
                e.stopPropagation();
                toggleEventTag(event, row);
                row.remove();

                // If no more tagged events, show the message
                if (taggedEvents.size === 0) {
                    document.getElementById('no-tagged-events').style.display = 'block';
                }
            });

            // Make the entire row clickable to show details
            row.addEventListener('click', function() {
                showEventDetailsInPanel(event);

                // Highlight the selected row
                const rows = taggedTableBody.querySelectorAll('tr');
                rows.forEach(r => r.classList.remove('table-primary'));
                row.classList.add('table-primary');
            });
        });
    }

    function clearAllTags() {
        taggedEvents.clear();
        updateTaggedCount();
        saveTaggedEvents();

        // Remove tagged-event class from all rows
        const rows = document.querySelectorAll('tr.tagged-event');
        rows.forEach(row => row.classList.remove('tagged-event'));

        // If we're in the tagged view, render it again
        if (document.getElementById('tagged-view').style.display !== 'none') {
            renderTaggedEvents();
        }
    }

    function saveTaggedEvents() {
        try {
            // Convert Map to array of objects for storage
            const eventsArray = Array.from(taggedEvents.values());
            localStorage.setItem('taggedEvents', JSON.stringify(eventsArray));
        } catch (error) {
            console.error('Error saving tagged events to localStorage:', error);
        }
    }

    function loadTaggedEvents() {
        try {
            const savedEvents = localStorage.getItem('taggedEvents');
            if (savedEvents) {
                const eventsArray = JSON.parse(savedEvents);

                // Clear existing tagged events
                taggedEvents.clear();

                // Add each event to the Map
                eventsArray.forEach(event => {
                    if (event && event.id) {
                        taggedEvents.set(event.id, event);
                    }
                });

                // Update the count
                updateTaggedCount();
            }
        } catch (error) {
            console.error('Error loading tagged events from localStorage:', error);
        }
    }

    // Load tagged events when the page loads
    loadTaggedEvents();
}
});